user root;
worker_processes 4;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}


http {
     gzip on;
      gzip_comp_level 5;
      gzip_min_length 1k;
      gzip_buffers 4 16k;
      gzip_proxied any;
      gzip_vary on;
      gzip_types
        application/javascript
        application/x-javascript
        text/javascript
        text/css
        text/xml
        application/xhtml+xml
        application/xml
        application/atom+xml
        application/rdf+xml
        application/rss+xml
        application/geo+json
        application/json
        application/ld+json
        application/manifest+json
        application/x-web-app-manifest+json
        image/svg+xml
        text/x-cross-domain-policy;
      gzip_disable "MSIE [1-6]\.";
        include       mime.types;
        default_type  application/octet-stream;

    sendfile        on;
    client_max_body_size 100M;
    # 日志格式定义
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # 默认access_log路径
    access_log /var/log/nginx/access.log main;

    add_header X-Frame-Options SAMEORIGIN;

    proxy_buffer_size   128k;
    proxy_buffers   4 256k;
    proxy_busy_buffers_size   256k;
    proxy_cache off;  # 关闭缓存
    proxy_buffering off;  # 关闭代理缓冲
    chunked_transfer_encoding on;  # 开启分块传输编码
    tcp_nopush on;  # 开启TCP NOPUSH选项，禁止Nagle算法
    tcp_nodelay on;  # 开启TCP NODELAY选项，禁止延迟ACK算法
    keepalive_timeout 600;  # 设定keep-alive超时时间为300秒
    keepalive_requests 100;

    server {
        listen 80;
        port_in_redirect off;
        add_header X-Content-Type-Options: nosniff;
        add_header X-XSS-Protection '1;mode=block';
        add_header Access-Control-Allow-Origin 'https://icir.quant-chi.com' always;
        add_header Access-Control-Allow-Methods "GET, POST, DELETE";
        error_page 403 =404 /404.html;
        location / {
            root /usr/local/web;
            index index.html;
            try_files $uri $uri/ /index.html;
        }

        location /preview {
            proxy_pass http://localhost:8012;
            client_max_body_size 100M;
        }

        location = /preview/index {
              return 403;  # 返回 403 Forbidden
          }

        location /api {
            client_max_body_size 100M;
            proxy_pass http://localhost:8612;

            # 添加代理头信息
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $host;

            # 超时设置
            proxy_connect_timeout 600s;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;

            # 代理缓冲设置
            proxy_buffer_size 16k;
            proxy_buffers 4 64k;
            proxy_busy_buffers_size 128k;
            proxy_temp_file_write_size 128k;
        }

      location /lz/ {
          # 代理到产业知识中心服务
          client_max_body_size 100M;
          proxy_pass http://localhost:7752/;
          proxy_set_header Host $host:$server_port;
      }

        # WebSocket专用配置
       location /api/ws/ {
           proxy_pass http://localhost:8612;

           # WebSocket必需的头信息
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;

           # WebSocket超时设置 - 增加超时时间以支持长时间报告生成
           proxy_connect_timeout 1800s;  # 30分钟连接超时
           proxy_send_timeout 1800s;     # 30分钟发送超时
           proxy_read_timeout 1800s;     # 30分钟读取超时

           # 禁用代理缓冲，WebSocket需要实时传输
           proxy_buffering off;
           proxy_cache off;

           # 增加缓冲区大小
           proxy_buffer_size 64k;
           proxy_buffers 32 32k;
           proxy_busy_buffers_size 128k;

           # 保持连接活跃
           proxy_set_header Connection "keep-alive";
       }


    location /api/qa/stream/chatWithThink {
             proxy_pass http://localhost:8612;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_read_timeout  1800;
             # 禁用 Nginx 的代理缓冲
             proxy_buffering off;
             # 启用分块传输编码 允许服务器分块发送数据，不需要预先知道内容长度
             chunked_transfer_encoding on;
             # 在发送数据时，尽可能将多个数据包组合成一个大的数据包 与 tcp_nodelay 搭配使用，Nginx 会自动在合适的时机启用合适的选项
             tcp_nopush on;
             # 禁用 Nagle 算法 允许小数据包立即发送，不等待累积
             tcp_nodelay on;
             # 禁用 gzip 压缩 避免压缩带来的延迟
             gzip off;
         }


        location /api/report/stream/chats {
               proxy_pass http://localhost:8612;
               proxy_set_header Host $http_host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;
               proxy_read_timeout  1800;
               # 禁用 Nginx 的代理缓冲
               proxy_buffering off;
               # 启用分块传输编码 允许服务器分块发送数据，不需要预先知道内容长度
               chunked_transfer_encoding on;
               # 在发送数据时，尽可能将多个数据包组合成一个大的数据包 与 tcp_nodelay 搭配使用，Nginx 会自动在合适的时机启用合适的选项
               tcp_nopush on;
               # 禁用 Nagle 算法 允许小数据包立即发送，不等待累积
               tcp_nodelay on;
               # 禁用 gzip 压缩 避免压缩带来的延迟
               gzip off;
           }

           location /api/sys/report/ai/generateStream {
                proxy_pass http://localhost:8612;
                proxy_set_header Host $http_host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_read_timeout  3600;
                # 禁用 Nginx 的代理缓冲
                proxy_buffering off;
                # 启用分块传输编码 允许服务器分块发送数据，不需要预先知道内容长度
                chunked_transfer_encoding on;
                # 在发送数据时，尽可能将多个数据包组合成一个大的数据包 与 tcp_nodelay 搭配使用，Nginx 会自动在合适的时机启用合适的选项
                tcp_nopush on;
                # 禁用 Nagle 算法 允许小数据包立即发送，不等待累积
                tcp_nodelay on;
                # 禁用 gzip 压缩 避免压缩带来的延迟
                gzip off;
            }
    }

    server {
            listen 7752;
            server_name _;
            add_header X-Content-Type-Options: nosniff;
            add_header X-XSS-Protection '1;mode=block';
            error_page 403 =404 /404.html;
            location / {
                root /usr/local/web/qa;
                index index.html;
                try_files $uri $uri/ /index.html;
            }
        }
}
#!/bin/bash
# 服务器端一键构建部署脚本 - 使用已有的 icir.jar 进行 Docker 构建和部署

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 配置信息
IMAGE_NAME="icir-backend"
IMAGE_TAG="latest"
CONTAINER_NAME="icir-backend"
PORT="host网络模式"
JAR_NAME="icir.jar"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

# 打印带颜色的日志
log_info() {
  echo -e "${GREEN}[INFO] $1${NC}"
}

log_warn() {
  echo -e "${YELLOW}[WARN] $1${NC}"
}

log_error() {
  echo -e "${RED}[ERROR] $1${NC}"
}

# 显示头部信息
log_info "========================================"
log_info "      ICIR 后端服务器构建部署脚本"
log_info "========================================"
log_info "脚本目录: ${SCRIPT_DIR}"
log_info "JAR文件: ${SCRIPT_DIR}/${JAR_NAME}"

# 1. 检查 JAR 文件是否存在
log_info "检查 JAR 文件..."
if [ ! -f "${SCRIPT_DIR}/${JAR_NAME}" ]; then
  log_error "JAR 文件 ${SCRIPT_DIR}/${JAR_NAME} 不存在!"
  log_error "请确保已将 icir.jar 上传到当前目录"
  exit 1
fi
log_info "JAR 文件检查通过"

# 2. 检查 Dockerfile 是否存在
if [ ! -f "${SCRIPT_DIR}/Dockerfile" ]; then
  log_error "Dockerfile 不存在!"
  exit 1
fi
log_info "Dockerfile 检查通过"

# 3. 停止并删除旧容器（如果存在）
if [ "$(docker ps -a -q -f name=$CONTAINER_NAME)" ]; then
  log_info "停止并删除旧容器..."
  docker stop $CONTAINER_NAME
  docker rm $CONTAINER_NAME
  log_info "旧容器已删除"
fi

# 4. 删除旧镜像（如果存在）
if [ "$(docker images -q $IMAGE_NAME:$IMAGE_TAG)" ]; then
  log_info "删除旧镜像..."
  docker rmi $IMAGE_NAME:$IMAGE_TAG
  log_info "旧镜像已删除"
fi

# 5. 构建 Docker 镜像
log_info "构建 Docker 镜像..."
cd "${SCRIPT_DIR}"
docker build --no-cache --pull=false -t ${IMAGE_NAME}:${IMAGE_TAG} .
if [ $? -ne 0 ]; then
  log_error "构建 Docker 镜像失败!"
  exit 1
fi
log_info "Docker 镜像构建成功"

# 6. 清理无标签的旧镜像
log_info "清理无标签的旧镜像..."
docker images | grep "<none>" | awk '{print $3}' | xargs -r docker rmi 2>/dev/null || true
log_info "无标签的旧镜像已清理"

# 7. 启动新容器
log_info "启动新容器..."
docker run -d --name $CONTAINER_NAME \
  --network host \
  --restart=always \
  $IMAGE_NAME:$IMAGE_TAG

if [ $? -ne 0 ]; then
  log_error "启动容器失败!"
  exit 1
fi

CONTAINER_ID=$(docker ps -q -f name=$CONTAINER_NAME)
log_info "容器启动成功，容器ID: $CONTAINER_ID"

# 8. 等待容器启动
log_info "等待容器启动..."
sleep 5

# 9. 检查容器状态
CONTAINER_STATUS=$(docker inspect --format='{{.State.Status}}' $CONTAINER_NAME)
if [ "$CONTAINER_STATUS" != "running" ]; then
  log_error "容器启动失败，状态: $CONTAINER_STATUS"
  log_error "容器日志:"
  docker logs $CONTAINER_NAME
  exit 1
fi

# 10. 显示部署结果
log_info "========================================"
log_info "部署完成!"
log_info "镜像名称: ${IMAGE_NAME}:${IMAGE_TAG}"
log_info "容器名称: ${CONTAINER_NAME}"
log_info "容器ID: ${CONTAINER_ID}"
log_info "端口映射: ${PORT}"
log_info "容器状态: ${CONTAINER_STATUS}"
log_info "========================================"

# 11. 显示容器日志
log_info "显示容器启动日志（按 Ctrl+C 退出日志查看）:"
docker logs -f $CONTAINER_NAME
package com.quantchi.knowledge.center.bean.enums;

import com.quantchi.knowledge.center.bean.exception.BusinessException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/5/22
 * es 索引
 */
@Getter
public enum EsIndexEnum {

    PATENT("patent", "专利", "icir_patent", "_doc", "text:title", "public_date", "id,name,chain_node,patent_type,public_code,public_date,abstract,inventors,status,inventors,applicants,download_name", "public_date:desc", "name.suggest", 10f),
    COMPANY("company", "企业", "icir_company", "_doc", "text/keyword:name", "establish_date", null, "score:desc", "name.suggest", 20.0f),
    NEWS("news", "资讯", "icir_news", "_doc", "text/keyword:title", "publish_date", null, null, "name.suggest", 10f),
    POLICY("policy", "政策", "icir_policy", "_doc", "text/keyword:title", "publish_date", null, null, null, 0.3f),
    REPORT("report", "研报", "icir_report", "_doc", null, null, null, null, null, 0.5f),
    FINANCING("financing", "投融资", "icir_financing", "_doc", "text/keyword:financing_company", "financing_date", null, null, null, 0.8f),
    ICD("icd", "ICD", "icir_icd", "_doc", "text/keyword:name", null, null, null, null, 1.0f),
    NODE("node", "产业", "icir_node", "_doc", "text/keyword:node_name", null, null, null, null, 1.0f),
    IPC("ipc", "技术", "icir_ipc", "_doc", "text/keyword:technology", null, null, null, null, 1.0f),
    PARK("park", "园区", "icir_park", "_doc", null, null, null, null, null, 0.5f),
    ;

    private final String index;

    private final String indexName;

    private final String esIndex;

    private final String esType;

    /**
     * 标题对应字段及格式
     */
    private final String titleColumn;

    private final String sort;

    private final String includeFields;

    /**
     * 挂接库搜索时的搜索顺序
     */
    private final String relateSort;

    private final String titleSuggestField;

    /**
     * 最小评分阈值，用于过滤低相关性的搜索结果
     */
    private final float minScore;

    EsIndexEnum(final String index, final String indexName, final String esIndex, final String esType, final String titleColumn, final String sort, final String includeFields, final String relateSort, final String titleSuggestField, final float minScore) {
        this.index = index;
        this.indexName = indexName;
        this.esIndex = esIndex;
        this.esType = esType;
        this.titleColumn = titleColumn;
        this.sort = sort;
        this.includeFields = includeFields;
        this.relateSort = relateSort;
        this.titleSuggestField = titleSuggestField;
        this.minScore = minScore;
    }

    public static EsIndexEnum getIndexEnumByEsIndex(final String esIndex) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getEsIndex().equals(esIndex)) {
                return value;
            }
        }
        return null;
    }

    public static EsIndexEnum getIndexEnumByType(final String index) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(index)) {
                return value;
            }
        }
        return null;
    }

    public static String getTitleSuggestFieldByType(final String type) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(type)) {
                return value.getTitleSuggestField();
            }
        }
        throw new BusinessException("这个类型" + type + "找不到对应的实际索引");
    }

    public static String getEsIndexByType(final String type) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(type)) {
                return value.getEsIndex();
            }
        }
        return type;
    }

    public static String getTypeByEsIndex(final String esIndex) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getEsIndex().equals(esIndex)) {
                return value.getIndex();
            }
        }
        throw new BusinessException("这个类型" + esIndex + "找不到对应的实际索引");
    }

    public static String getIndexNameByType(final String type) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(type)) {
                return value.getIndexName();
            }
        }
        return null;
    }

    public static String getIndexNameByIndex(final String index) {
        return getIndexNameByType(index);
    }

    /**
     * 根据索引名称获取最小评分阈值
     *
     * @param index 索引名称
     * @return 最小评分阈值，如果找不到对应索引则返回默认值 0.0001f
     */
    public static float getMinScoreByIndex(final String index) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(index)) {
                return value.getMinScore();
            }
        }
        return 0.0001f; // 默认最小评分
    }

    /**
     * 根据 ES 索引名称获取最小评分阈值
     *
     * @param esIndex ES 索引名称
     * @return 最小评分阈值，如果找不到对应索引则返回默认值 0.0001f
     */
    public static float getMinScoreByEsIndex(final String esIndex) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getEsIndex().equals(esIndex)) {
                return value.getMinScore();
            }
        }
        return 0.0001f; // 默认最小评分
    }

}

package com.quantchi.knowledge.center.service.index;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.knowledge.ai.bo.GeoQueryBO;
import com.quantchi.knowledge.center.bean.constant.Constants;
import com.quantchi.knowledge.center.bean.entity.Company;
import com.quantchi.knowledge.center.bean.entity.CompanyTagSchema;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.export.IcdLibraryCompanyListExport;
import com.quantchi.knowledge.center.bean.export.IpcLibraryCompanyListExport;
import com.quantchi.knowledge.center.bean.model.*;
import com.quantchi.knowledge.center.config.properties.KeywordSearchProperties;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.ICompanyService;
import com.quantchi.knowledge.center.service.ICompanyTagSchemaService;
import com.quantchi.knowledge.center.service.IDmDivisionService;
import com.quantchi.knowledge.center.service.impl.LibraryInfoService;
import com.quantchi.knowledge.center.util.ElasticsearchBuilder;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.geo.GeoDistance;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyService extends LibraryInfoService {

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private ThreadPoolTaskExecutor docExecutor;

    @Autowired
    private ICompanyTagSchemaService companyTagSchemaService;

    public CompanyService(final ElasticsearchHelper elasticsearchHelper, final NavigationSettings navigationSettings,
            final KeywordSearchProperties keywordSearchProperties, final IDmDivisionService dmDivisionService) {
        super(elasticsearchHelper, navigationSettings, keywordSearchProperties, dmDivisionService);
    }

    /**
     * 实现特定于企业索引的查询逻辑
     * 
     * @param index             索引名称
     * @param processedKeyword  关键词
     * @param boolQuery         查询条件构建器
     * @param highlightBuilder  高亮构建器
     * @param keywordSearchList 关键词搜索属性列表
     * @return 如果处理了该索引的查询逻辑返回true，否则返回false
     */
    @Override
    protected boolean applySpecificKeywordQuery(String index,
            String processedKeyword,
            BoolQueryBuilder boolQuery,
            HighlightBuilder highlightBuilder,
            List<KeywordSearchProperty> keywordSearchList) {
        // 仅处理企业索引
        if (!EsIndexEnum.COMPANY.getEsIndex().equals(index)) {
            return false;
        }

        // 企业索引特定处理逻辑
        List<KeywordSearchProperty> adjustedKeywordSearchList = adjustCompanySearchWeights(keywordSearchList);

        // 构建基础查询
        final BasicBoolQuery queryBO = new BasicBoolQuery();
        queryBO.setKeyword(processedKeyword);
        queryBO.setKeywordFields(adjustedKeywordSearchList);
        queryBO.setOperator(Operator.AND);
        queryBO.setBoolQueryBuilder(boolQuery);
        queryBO.setIndex(index);
        ElasticsearchBuilder.buildBoolQuery(queryBO);

        // 添加企业名称搜索，智能分配关键词权重
        BoolQueryBuilder nameQueryBuilder = QueryBuilders.boolQuery();

        // 优先级1: 精确匹配 name.term - 大幅提高权重确保精确匹配排在最前
        TermQueryBuilder exactMatchTermQuery = QueryBuilders.termQuery("name.term", processedKeyword)
                .boost(500.0f); // 大幅提高精确匹配权重

        // 优先级2: 短语匹配 name - 大幅提高权重确保包含完整关键词的企业排前
        MatchPhraseQueryBuilder phraseMatchQuery = QueryBuilders.matchPhraseQuery("name", processedKeyword)
                .boost(400.0f); // 大幅提高短语匹配权重

        // 优先级3: 曾用名精确匹配 - 高权重
        MatchPhraseQueryBuilder usedNamePhraseQuery = QueryBuilders.matchPhraseQuery("used_name", processedKeyword)
                .boost(300.0f); // 提高曾用名匹配权重

        // 添加智能关键词权重分配 - 分析关键词并给予不同权重
        addSmartKeywordQueries(nameQueryBuilder, processedKeyword);

        // 优先级4: Ngram匹配 name.ngram - 降低权重避免过多模糊匹配
        MatchQueryBuilder ngramMatchQuery = QueryBuilders.matchQuery("name.ngram", processedKeyword)
                .boost(10.0f);

        // 优先级5: IK分词匹配 name - 进一步提高最小匹配度，降低权重
        MatchQueryBuilder ikMatchQuery = QueryBuilders.matchQuery("name", processedKeyword)
                .minimumShouldMatch("95%") // 进一步提高最小匹配度从90%到95%，减少不相关匹配
                .boost(1.5f); // 进一步降低权重

        // 优先级6: 法人搜索 - 保持较低权重
        MatchPhraseQueryBuilder legalPersonQuery = QueryBuilders.matchPhraseQuery("legal_person", processedKeyword)
                .boost(1.0f);

        nameQueryBuilder.should(exactMatchTermQuery);
        nameQueryBuilder.should(phraseMatchQuery);
        nameQueryBuilder.should(usedNamePhraseQuery);
        nameQueryBuilder.should(ngramMatchQuery);
        nameQueryBuilder.should(ikMatchQuery);
        nameQueryBuilder.should(legalPersonQuery);

        // 确保 name/used_name/legal_person 中至少有一个条件匹配
        nameQueryBuilder.minimumShouldMatch(1);

        boolQuery.must(nameQueryBuilder);

        // 添加完全匹配的额外权重查询，确保精确匹配的企业排在最前面
        BoolQueryBuilder exactMatchBoostQuery = QueryBuilders.boolQuery();
        exactMatchBoostQuery.should(QueryBuilders.termQuery("name.term", processedKeyword).boost(200.0f));
        exactMatchBoostQuery.should(QueryBuilders.matchPhraseQuery("name", processedKeyword).boost(150.0f));
        exactMatchBoostQuery.minimumShouldMatch(0);
        boolQuery.should(exactMatchBoostQuery);

        // 添加地区词抑制机制 - 如果查询包含地区词，降低仅匹配地区词的企业权重
        addRegionSuppressionMechanism(boolQuery, processedKeyword);

        // 设置企业状态过滤条件，优先显示正常经营的企业
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery();
        statusQueryBuilder.should(QueryBuilders.termQuery("status", "在业").boost(50.0f));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", "存续").boost(45.0f));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", "开业").boost(20.0f));
        boolQuery.should(statusQueryBuilder);

        // 使用must_not来排除注销企业，或者使用filter降低其权重
        BoolQueryBuilder cancelledStatusQueryBuilder = QueryBuilders.boolQuery();
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "注销"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "吁销"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "除名"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "撤销"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "责令关闭"));
        cancelledStatusQueryBuilder.should(QueryBuilders.termQuery("status", "已歇业"));

        // 使用must_not将注销企业排除，确保正常企业优先显示
        boolQuery.mustNot(cancelledStatusQueryBuilder);

        // 将不同类型的条件分开处理，以便更精确地控制各个因素的权重

        // 1. 企业标签和规模 - 降低权重，避免影响关键词匹配的主导地位
        BoolQueryBuilder tagsQueryBuilder = QueryBuilders.boolQuery();
        tagsQueryBuilder.should(QueryBuilders.existsQuery("tags").boost(5.0f)); // 大幅降低
        tagsQueryBuilder.should(QueryBuilders.termQuery("company_scale", "大型").boost(3.0f)); // 大幅降低
        // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
        tagsQueryBuilder.minimumShouldMatch(0);
        boolQuery.should(tagsQueryBuilder.boost(0.5f)); // 降低整体权重
        boolQuery.should(QueryBuilders.termQuery("tags.name", "国家级").boost(8.0f)); // 大幅降低

        // 2. 基于标签数量的权重提升 - 标签越多排序越靠前
        addTagCountBoostQuery(boolQuery);

        // 3. 技术得分 - 降低权重，避免影响关键词匹配
        boolQuery.should(QueryBuilders.rangeQuery("technical_score").gte(90).boost(8.0f)); // 大幅降低
        BoolQueryBuilder technicalScoreQueryBuilder = QueryBuilders.boolQuery();
        technicalScoreQueryBuilder.should(QueryBuilders.rangeQuery("technical_score").gte(80).lt(90).boost(6.0f));
        technicalScoreQueryBuilder.should(QueryBuilders.rangeQuery("technical_score").gte(70).lt(80).boost(4.0f));
        technicalScoreQueryBuilder.should(QueryBuilders.rangeQuery("technical_score").gte(60).lt(70).boost(2.0f));
        technicalScoreQueryBuilder.should(QueryBuilders.existsQuery("technical_score").boost(1.0f));
        // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
        technicalScoreQueryBuilder.minimumShouldMatch(0);
        boolQuery.should(technicalScoreQueryBuilder.boost(0.3f)); // 大幅降低整体权重

        // 4. 商业得分 - 降低权重，避免影响关键词匹配
        boolQuery.should(QueryBuilders.rangeQuery("business_score").gte(90).boost(10.0f)); // 大幅降低
        BoolQueryBuilder businessScoreQueryBuilder = QueryBuilders.boolQuery();
        businessScoreQueryBuilder.should(QueryBuilders.rangeQuery("business_score").gte(80).lt(90).boost(8.0f));
        businessScoreQueryBuilder.should(QueryBuilders.rangeQuery("business_score").gte(70).lt(80).boost(6.0f));
        businessScoreQueryBuilder.should(QueryBuilders.rangeQuery("business_score").gte(60).lt(70).boost(4.0f));
        businessScoreQueryBuilder.should(QueryBuilders.existsQuery("business_score").boost(2.0f));
        // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
        businessScoreQueryBuilder.minimumShouldMatch(0);
        boolQuery.should(businessScoreQueryBuilder.boost(0.3f)); // 大幅降低整体权重

        // 5. 网站和Logo - 降低权重，避免影响关键词匹配
        BoolQueryBuilder webInfoQueryBuilder = QueryBuilders.boolQuery();
        webInfoQueryBuilder.should(QueryBuilders.existsQuery("website").boost(3.0f)); // 大幅降低
        webInfoQueryBuilder.should(QueryBuilders.existsQuery("logo_source").boost(2.0f)); // 大幅降低
        // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
        webInfoQueryBuilder.minimumShouldMatch(0);
        boolQuery.should(webInfoQueryBuilder.boost(0.2f)); // 大幅降低整体权重

        return true;
    }

    /**
     * 智能关键词权重分配
     * 分析关键词，对地区词降权，对企业特有词提权
     *
     * @param nameQueryBuilder 名称查询构建器
     * @param keyword 搜索关键词
     */
    private void addSmartKeywordQueries(final BoolQueryBuilder nameQueryBuilder, final String keyword) {
        // 常见地区词列表（可以扩展）
        final String[] regionWords = {
            "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "武汉", "西安",
            "天津", "重庆", "青岛", "大连", "宁波", "厦门", "福州", "长沙", "郑州", "济南",
            "沈阳", "哈尔滨", "长春", "石家庄", "太原", "呼和浩特", "兰州", "西宁", "银川",
            "乌鲁木齐", "拉萨", "昆明", "贵阳", "南宁", "海口", "三亚", "合肥", "南昌",
            "市", "区", "县", "镇", "街道", "开发区", "高新区", "经济区", "工业区"
        };

        // 分析关键词，分离地区词和企业特有词
        String[] words = keyword.split("\\s+|(?<=[\u4e00-\u9fa5])(?=[\u4e00-\u9fa5])|(?<=\\w)(?=\\w)");

        List<String> regionWordsInKeyword = new ArrayList<>();
        List<String> nonRegionWords = new ArrayList<>();

        for (String word : words) {
            if (StrUtil.isBlank(word) || word.length() < 2) {
                continue;
            }

            boolean isRegionWord = false;
            for (String regionWord : regionWords) {
                if (word.contains(regionWord) || regionWord.contains(word)) {
                    isRegionWord = true;
                    regionWordsInKeyword.add(word);
                    break;
                }
            }

            if (!isRegionWord) {
                nonRegionWords.add(word);
            }
        }

        // 处理企业特有词 - 大幅提高权重
        for (String word : nonRegionWords) {
            // 企业特有词使用极高权重，确保主导地位
            nameQueryBuilder.should(QueryBuilders.matchPhraseQuery("name", word).boost(200.0f));
            nameQueryBuilder.should(QueryBuilders.matchQuery("name", word).boost(150.0f));
            // 对于企业特有词，也在曾用名中搜索
            nameQueryBuilder.should(QueryBuilders.matchPhraseQuery("used_name", word).boost(180.0f));
        }

        // 处理地区词 - 极低权重
        for (String word : regionWordsInKeyword) {
            // 地区词使用极低的权重
            nameQueryBuilder.should(QueryBuilders.matchPhraseQuery("name", word).boost(0.5f));
            nameQueryBuilder.should(QueryBuilders.matchQuery("name", word).boost(0.3f));
        }

        // 如果关键词包含多个非地区词，极大提升组合查询权重
        if (nonRegionWords.size() > 1) {
            BoolQueryBuilder multiWordQuery = QueryBuilders.boolQuery();
            for (String word : nonRegionWords) {
                multiWordQuery.must(QueryBuilders.matchQuery("name", word));
            }
            // 极大提升多词组合权重，确保包含所有企业特有词的公司排在前面
            nameQueryBuilder.should(multiWordQuery.boost(600.0f));

            // 添加曾用名的多词组合查询
            BoolQueryBuilder multiWordUsedNameQuery = QueryBuilders.boolQuery();
            for (String word : nonRegionWords) {
                multiWordUsedNameQuery.must(QueryBuilders.matchQuery("used_name", word));
            }
            nameQueryBuilder.should(multiWordUsedNameQuery.boost(500.0f));
        }

        // 如果同时包含地区词和企业特有词，添加地区抑制机制
        if (!regionWordsInKeyword.isEmpty() && !nonRegionWords.isEmpty()) {
            // 创建一个查询，优先匹配包含所有非地区词但地区词权重很低的企业
            BoolQueryBuilder regionSuppressionQuery = QueryBuilders.boolQuery();

            // 必须包含所有企业特有词
            for (String word : nonRegionWords) {
                regionSuppressionQuery.must(QueryBuilders.matchQuery("name", word));
            }

            // 地区词作为可选条件，但权重极低
            BoolQueryBuilder regionOptionalQuery = QueryBuilders.boolQuery();
            for (String word : regionWordsInKeyword) {
                regionOptionalQuery.should(QueryBuilders.matchQuery("name", word).boost(0.5f));
            }
            regionOptionalQuery.minimumShouldMatch(0);
            regionSuppressionQuery.should(regionOptionalQuery);

            // 给这种查询极高的权重，确保精确匹配企业特有词的公司排在前面
            nameQueryBuilder.should(regionSuppressionQuery.boost(800.0f));
        }
    }

    /**
     * 添加地区词抑制机制
     * 当查询包含地区词时，降低仅匹配地区词的企业权重
     *
     * @param boolQuery 主查询构建器
     * @param keyword 搜索关键词
     */
    private void addRegionSuppressionMechanism(final BoolQueryBuilder boolQuery, final String keyword) {
        // 常见地区词列表
        final String[] regionWords = {
            "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "武汉", "西安",
            "天津", "重庆", "青岛", "大连", "宁波", "厦门", "福州", "长沙", "郑州", "济南",
            "沈阳", "哈尔滨", "长春", "石家庄", "太原", "呼和浩特", "兰州", "西宁", "银川",
            "乌鲁木齐", "拉萨", "昆明", "贵阳", "南宁", "海口", "三亚", "合肥", "南昌",
            "市", "区", "县", "镇", "街道", "开发区", "高新区", "经济区", "工业区"
        };

        // 检查关键词是否包含地区词
        boolean containsRegionWord = false;
        for (final String regionWord : regionWords) {
            if (keyword.contains(regionWord)) {
                containsRegionWord = true;
                break;
            }
        }

        // 如果包含地区词，添加抑制机制
        if (containsRegionWord) {
            // 创建一个查询，专门匹配仅包含地区词的企业，并给予负向权重影响
            BoolQueryBuilder regionOnlyQuery = QueryBuilders.boolQuery();

            // 检查是否主要匹配地区词
            for (final String regionWord : regionWords) {
                if (keyword.contains(regionWord)) {
                    // 如果企业名称主要匹配地区词，降低其权重
                    BoolQueryBuilder regionMatchQuery = QueryBuilders.boolQuery();
                    regionMatchQuery.must(QueryBuilders.matchQuery("name", regionWord));

                    // 检查是否不包含其他重要词汇（通过最小匹配度控制）
                    String nonRegionPart = keyword.replace(regionWord, "").trim();
                    if (StrUtil.isNotBlank(nonRegionPart)) {
                        // 如果不包含非地区词，则降低权重
                        regionMatchQuery.mustNot(QueryBuilders.matchQuery("name", nonRegionPart).minimumShouldMatch("50%"));
                    }

                    // 给仅匹配地区词的企业很低的权重
                    regionOnlyQuery.should(regionMatchQuery.boost(0.1f));
                }
            }

            if (regionOnlyQuery.hasClauses()) {
                regionOnlyQuery.minimumShouldMatch(0);
                // 使用should而不是mustNot，这样不会完全排除，只是降低权重
                boolQuery.should(regionOnlyQuery);
            }
        }
    }

    /**
     * 添加基于标签数量的权重提升查询
     * 优化版本：避免script query，使用更高效的查询方式
     *
     * @param boolQuery 主查询构建器
     */
    private void addTagCountBoostQuery(final BoolQueryBuilder boolQuery) {
        // 方案：使用预计算字段 + range query（推荐在ES索引中添加tag_count字段）
        // 如果ES索引中有tag_count字段，使用以下代码：
        /*
         * boolQuery.should(QueryBuilders.rangeQuery("tag_count").gte(5).boost(200.0f));
         * boolQuery.should(QueryBuilders.rangeQuery("tag_count").gte(4).lt(5).boost(150
         * .0f));
         * boolQuery.should(QueryBuilders.rangeQuery("tag_count").gte(3).lt(4).boost(100
         * .0f));
         * boolQuery.should(QueryBuilders.rangeQuery("tag_count").gte(2).lt(3).boost(50.
         * 0f));
         * boolQuery.should(QueryBuilders.rangeQuery("tag_count").gte(1).lt(2).boost(20.
         * 0f));
         */

        // 临时方案：使用标签存在性和高价值标签权重
        // 这种方式性能好，但不能精确按标签数量排序

        // 1. 基础标签存在权重 - 大幅降低权重，避免影响关键词匹配
        boolQuery.should(QueryBuilders.existsQuery("tags").boost(2.0f)); // 大幅降低

        // 2. 高价值标签额外权重 - 大幅降低权重
        final String[] highValueTags = {
                "高新技术企业", "专精特新企业", "专精特新小巨人企业",
                "企业技术中心", "独角兽企业", "科技小巨人", "隐形冠军企业", "浙江大学", "创新型中小企业", "科技型中小企业"
        };

        for (final String tag : highValueTags) {
            boolQuery.should(QueryBuilders.termQuery("tags.name", tag).boost(3.0f)); // 大幅降低
        }

        // 3. 上市企业相关标签 - 大幅降低权重
        final String[] listingTags = { "A股", "B股", "港股", "中概股" };
        BoolQueryBuilder listingQuery = QueryBuilders.boolQuery();
        for (final String tag : listingTags) {
            listingQuery.should(QueryBuilders.termQuery("tags.name", tag));
        }
        listingQuery.minimumShouldMatch(1);
        boolQuery.should(listingQuery.boost(5.0f)); // 大幅降低

        // 4. 多标签组合检测 - 大幅降低权重
        BoolQueryBuilder multiCategoryQuery = QueryBuilders.boolQuery();
        multiCategoryQuery.should(QueryBuilders.termQuery("tags.name", "高新技术企业"));
        multiCategoryQuery.should(QueryBuilders.termsQuery("tags.name", listingTags));
        multiCategoryQuery.should(QueryBuilders.termQuery("tags.name", "专精特新企业"));
        multiCategoryQuery.minimumShouldMatch(2); // 至少匹配2个不同类型
        boolQuery.should(multiCategoryQuery.boost(4.0f)); // 大幅降低
    }

    @PostConstruct
    @Override
    public void init() {
        libraryInfoServiceMap.put(EsIndexEnum.COMPANY.getEsIndex(), this);
        libraryInfoServiceMap.put(EsIndexEnum.COMPANY.getIndex(), this);
    }

    @Override
    public void specialBuildBoolQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries,
            final GeoQueryBO geoQuery) {
        generateGeoQuery(boolQuery, geoQuery);
        generateChainQuery(boolQuery, termQueries);
        generateLabelsQuery(boolQuery, termQueries);
        generateChainNodeQuery(boolQuery, termQueries);
        boolQuery.filter(QueryBuilders.existsQuery("name"));
    }

    /**
     * 生成标签查询条件
     */
    private void generateLabelsQuery(final BoolQueryBuilder boolQuery, final Map<String, List<String>> termQueries) {
        if (termQueries != null && termQueries.containsKey("labels")) {
            final List<String> labels = termQueries.get("labels");
            if (CollUtil.isNotEmpty(labels)) {
                boolQuery.filter(QueryBuilders.termsQuery("tags.name", labels));
            }
            termQueries.remove("labels");
        }
        if (termQueries != null && termQueries.containsKey("plate")) {
            final List<String> plate = termQueries.get("plate");
            if (CollUtil.isNotEmpty(plate)) {
                boolQuery.filter(QueryBuilders.termsQuery("tags.name", plate));
            }
            termQueries.remove("plate");
        }
        if (termQueries != null && termQueries.containsKey("capital")) {
            final List<String> capital = termQueries.get("capital");
            if (CollUtil.isNotEmpty(capital)) {
                final List<CompanyTagSchema> list = companyTagSchemaService
                        .list(Wrappers.<CompanyTagSchema>lambdaQuery()
                                .in(CompanyTagSchema::getParentId, capital)
                                .or()
                                .in(CompanyTagSchema::getId, capital));
                boolQuery.filter(QueryBuilders.termsQuery("tags.id", list.stream().map(CompanyTagSchema::getId)
                        .collect(Collectors.toList())));
            }
            termQueries.remove("capital");
        }
        if (termQueries != null && termQueries.containsKey("rank")) {
            final List<String> rank = termQueries.get("rank");
            if (CollUtil.isNotEmpty(rank)) {
                final List<CompanyTagSchema> list = companyTagSchemaService
                        .list(Wrappers.<CompanyTagSchema>lambdaQuery()
                                .in(CompanyTagSchema::getParentId, rank)
                                .or()
                                .in(CompanyTagSchema::getId, rank));
                boolQuery.filter(QueryBuilders.termsQuery("tags.id", list.stream().map(CompanyTagSchema::getId)
                        .collect(Collectors.toList())));
            }
            termQueries.remove("rank");
        }

    }

    /**
     * 根据地理位置条件生成查询参数
     */
    private void generateGeoQuery(final BoolQueryBuilder boolQuery, final GeoQueryBO geoQuery) {
        if (geoQuery != null) {
            // 构建半径查询 GeoDistanceQueryBuilder
            final GeoDistanceQueryBuilder geoDistanceQueryBuilder = QueryBuilders.geoDistanceQuery("location")
                    .point(Double.parseDouble(geoQuery.getLatitude()), Double.parseDouble(geoQuery.getLongitude()))
                    .distance(geoQuery.getRadius(), DistanceUnit.KILOMETERS)
                    .geoDistance(GeoDistance.PLANE);
            boolQuery.filter(geoDistanceQueryBuilder);
        }
    }

    @Override
    protected void specialSearchSourceForLibrarySearch(final String esIndex, final SearchSourceBuilder searchSource,
            final Map<String, List<String>> termQueries, final String keyword, final BoolQueryBuilder boolQuery) {

    }

    @Override
    public String specialSortForLibrarySearch(final BoolQueryBuilder boolQuery, final String sort,
            final String keyword) {
        // 未做搜索的时候企业库增加经营评价降序排列
        if (StrUtil.isEmpty(keyword)) {
            if (StrUtil.isBlank(sort)) {
                return "business_score:desc";
            }
        }
        return sort;
    }

    @Override
    public EsPageResult specialDealWithLibraryPageResult(final EsPageResult pageResult) {
        final List<Map<String, Object>> list = pageResult.getList();
        if (CollUtil.isEmpty(list)) {
            return pageResult;
        }
        final List<String> companyIdList = new ArrayList<>(list.size());
        for (final Map<String, Object> sourceAsMap : list) {
            companyIdList.add((String) sourceAsMap.get("id"));
            final Object tags = sourceAsMap.get("tags");
            if (tags instanceof List) {
                final Set<String> labelSet = new HashSet<>();
                ((List) tags).forEach(
                        tag -> {
                            if (tag instanceof Map) {
                                final Object tagObject = ((Map) tag).get("name");
                                if (tagObject != null) {
                                    labelSet.add((String) tagObject);
                                }
                            }
                        });
                sourceAsMap.remove("tags");
                sourceAsMap.put("labels", labelSet);
            }
            sourceAsMap.put("region", Constants.getBelongArea((String) sourceAsMap.get("province"),
                    (String) sourceAsMap.get("city"), null));
        }
        // 企业库额外查询字段
        final List<Company> companies = companyService.listByIds(companyIdList);
        final Map<String, Company> map = companies.stream()
                .collect(Collectors.toMap(Company::getId, Function.identity(), (v1, v2) -> v1));
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(list.size());

        // 检查是否为导出请求
        final boolean isForExport;
        Object requestContext = pageResult.getRequestContext();
        if (requestContext instanceof MultidimensionalQuery) {
            MultidimensionalQuery query = (MultidimensionalQuery) requestContext;
            isForExport = Boolean.TRUE.equals(query.getIsForExport());
        } else {
            isForExport = false;
        }

        for (final Map<String, Object> sourceAsMap : list) {
            futureList.add(CompletableFuture.runAsync(() -> {
                if (map.containsKey((String) sourceAsMap.get("id"))) {
                    final String id = (String) sourceAsMap.get("id");
                    final Company company = map.get(id);

                    // 普通查询时只返回基本字段
                    if (!isForExport) {
                        sourceAsMap.put("regist_capi", company.getRegistCapi());
                        sourceAsMap.put("logo_source", company.getLogoSource());
                        sourceAsMap.put("address", company.getAddress());
                        sourceAsMap.put("tel", company.getTel());
                        sourceAsMap.put("website", company.getWebsite());
                    } else {
                        // 导出时返回所有需要的字段
                        sourceAsMap.put("name_en", company.getNameEn());
                        sourceAsMap.put("used_name", company.getUsedName());
                        sourceAsMap.put("credit_code", company.getCreditCode());
                        sourceAsMap.put("legal_person", company.getLegalPerson());
                        sourceAsMap.put("logo", company.getLogo());
                        sourceAsMap.put("logo_source", company.getLogoSource());
                        sourceAsMap.put("employee_size", company.getEmployeeSize());
                        sourceAsMap.put("insured_number", company.getInsuredNumber());
                        // valid_phone field exists in the database but not in the entity class
                        // sourceAsMap.put("valid_phone", company.getValidPhone());
                        sourceAsMap.put("tel", company.getTel());
                        sourceAsMap.put("email", company.getEmail());
                        sourceAsMap.put("website", company.getWebsite());
                        sourceAsMap.put("address", company.getAddress());
                        // contact_address field exists in the database but not in the entity class
                        // sourceAsMap.put("contact_address", company.getContactAddress());
                        sourceAsMap.put("establish_date", company.getEstablishDate());
                        sourceAsMap.put("nation", company.getNation());
                        sourceAsMap.put("province", company.getProvince());
                        sourceAsMap.put("city", company.getCity());
                        sourceAsMap.put("area", company.getArea());
                        sourceAsMap.put("town", company.getTown());
                        sourceAsMap.put("nation_code", company.getNationCode());
                        sourceAsMap.put("province_code", company.getProvinceCode());
                        sourceAsMap.put("city_code", company.getCityCode());
                        sourceAsMap.put("area_code", company.getAreaCode());
                        sourceAsMap.put("lng", company.getLng());
                        sourceAsMap.put("lat", company.getLat());
                        sourceAsMap.put("check_date", company.getCheckDate());
                        sourceAsMap.put("status", company.getStatus());
                        sourceAsMap.put("pay_capi", company.getPayCapi());
                        sourceAsMap.put("pay_capi_value", company.getPayCapiValue());
                        sourceAsMap.put("pay_capi_unit", company.getPayCapiUnit());
                        sourceAsMap.put("pay_capi_value_cal", company.getPayCapiValueCal());
                        sourceAsMap.put("regist_capi", company.getRegistCapi());
                        sourceAsMap.put("regist_capi_value", company.getRegistCapiValue());
                        sourceAsMap.put("regist_capi_unit", company.getRegistCapiUnit());
                        sourceAsMap.put("regist_capi_value_cal", company.getRegistCapiValueCal());
                        sourceAsMap.put("org_no", company.getOrgNo());
                        sourceAsMap.put("company_type", company.getCompanyType());
                        sourceAsMap.put("company_scale", company.getCompanyScale());
                        sourceAsMap.put("business_term", company.getBusinessTerm());
                        sourceAsMap.put("taxpayer_no", company.getTaxpayerNo());
                        sourceAsMap.put("nation_industry_1", company.getNationIndustry1());
                        sourceAsMap.put("nation_industry_2", company.getNationIndustry2());
                        sourceAsMap.put("nation_industry_3", company.getNationIndustry3());
                        sourceAsMap.put("nation_industry_4", company.getNationIndustry4());
                        sourceAsMap.put("nation_industry_code", company.getNationIndustryCode());
                        sourceAsMap.put("belong_org", company.getBelongOrg());
                        sourceAsMap.put("registration_id", company.getRegistrationId());
                        sourceAsMap.put("business_scope", company.getBusinessScope());
                        sourceAsMap.put("description", company.getDescription());
                    }
                }
            }, docExecutor));
        }

        final CompletableFuture<Void> allFutures = CompletableFuture
                .allOf(futureList.toArray(new CompletableFuture[list.size()]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }

        dealWithSecName(list);
        dealWithFecName(list);
        dealWithNecName(list);
        return pageResult;
    }

    @Override
    public File icdDataExport(final EsPageResult esPageResult, final HttpServletResponse response) throws IOException {
        // 构建数据
        final List<Map<String, Object>> list = esPageResult.getList();
        final List<IcdLibraryCompanyListExport> dataList = new ArrayList<>();
        for (final Map<String, Object> sourceAsMap : list) {
            final IcdLibraryCompanyListExport export = new IcdLibraryCompanyListExport();
            export.setName((String) sourceAsMap.get("name"));
            final Object labels = sourceAsMap.get("labels");
            if (labels instanceof List) {
                export.setLabels(StrUtil.join(";", (List<String>) labels));
            } else if (labels instanceof Set) {
                export.setLabels(StrUtil.join(";", (Set<String>) labels));
            }
            export.setLegalPerson((String) sourceAsMap.get("legal_person"));
            export.setEstablishDate((String) sourceAsMap.get("establish_date"));
            export.setRegistCapi((String) sourceAsMap.get("regist_capi"));
            export.setRegion(Constants.getBelongArea((String) sourceAsMap.get("province"),
                    (String) sourceAsMap.get("city"), null));
            dataList.add(export);
        }
        return listExport(dataList, response, "企业明细导出表", IcdLibraryCompanyListExport.class);
    }

    @Override
    public File ipcDataExport(final EsPageResult esPageResult, final HttpServletResponse response) throws IOException {
        // 构建数据
        final List<Map<String, Object>> list = esPageResult.getList();
        final List<IpcLibraryCompanyListExport> dataList = new ArrayList<>();
        for (final Map<String, Object> sourceAsMap : list) {
            final IpcLibraryCompanyListExport export = new IpcLibraryCompanyListExport();
            export.setName((String) sourceAsMap.get("name"));
            final Object labels = sourceAsMap.get("labels");
            if (labels instanceof List) {
                export.setLabels(StrUtil.join(";", (List<String>) labels));
            } else if (labels instanceof Set) {
                export.setLabels(StrUtil.join(";", (Set<String>) labels));
            }
            export.setLegalPerson((String) sourceAsMap.get("legal_person"));
            export.setEstablishDate((String) sourceAsMap.get("establish_date"));
            export.setRegistCapi((String) sourceAsMap.get("regist_capi"));
            export.setRegion(Constants.getBelongArea((String) sourceAsMap.get("province"),
                    (String) sourceAsMap.get("city"), null));
            export.setPatentOwnershipCount(String.valueOf(sourceAsMap.get("patentOwnershipCount")));
            dataList.add(export);
        }
        return listExport(dataList, response, "企业明细导出表", IpcLibraryCompanyListExport.class);
    }

}
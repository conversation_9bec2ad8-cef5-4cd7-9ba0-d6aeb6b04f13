package com.quantchi.knowledge.ai.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * oauth2客户端信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "oauth2认证信息")
public class Oauth2ClientModelVO {


    @ApiModelProperty("客户端id")
    private Long clientId;

    @ApiModelProperty("客户端秘钥")
    private String clientSecret;

    @ApiModelProperty("openId")
    private String openId;

    @ApiModelProperty("应用允许授权的所有url，多个用逗号隔开")
    private String allowUrl;

    @ApiModelProperty("应用签约的所有权限，多个用逗号隔开")
    private String contractScope;

    @ApiModelProperty("组件id")
    private Long componentId;

    @ApiModelProperty("所属用户id")
    private Long loginId;

}

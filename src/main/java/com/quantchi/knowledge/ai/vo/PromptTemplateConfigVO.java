package com.quantchi.knowledge.ai.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.quantchi.knowledge.center.bean.entity.PromptTemplateConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * <p>
 * 提示模版配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@ApiModel(value = "提示模版展示类")
public class PromptTemplateConfigVO implements Serializable {

    private Integer id;

    @ApiModelProperty("标题")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String title;

    @ApiModelProperty("内容")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String content;

    @ApiModelProperty("图片链接")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String imgUrl;

    @ApiModelProperty("文件链接")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String fileUrl;

    @ApiModelProperty("素材种类 1文本/2图片/3文本加图片")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer materialType;

    @ApiModelProperty("种类，如政策、画像等")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String category;

    public static PromptTemplateConfigVO toVO(final PromptTemplateConfig config) {
        final PromptTemplateConfigVO vo = new PromptTemplateConfigVO();
        BeanUtils.copyProperties(config, vo);
        return vo;
    }
}

package com.quantchi.knowledge.ai.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.quantchi.knowledge.center.bean.entity.PromptTemplateConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * <p>
 * 提示模版配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@ApiModel(value = "问答的提示模版展示类")
public class PromptTemplateConfigQaVO implements Serializable {

    @ApiModelProperty("标题")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String title;

    @ApiModelProperty("内容")
    private String content;

    public static PromptTemplateConfigQaVO toVO(final PromptTemplateConfig config) {
        final PromptTemplateConfigQaVO vo = new PromptTemplateConfigQaVO();
        BeanUtils.copyProperties(config, vo);
        return vo;
    }
}

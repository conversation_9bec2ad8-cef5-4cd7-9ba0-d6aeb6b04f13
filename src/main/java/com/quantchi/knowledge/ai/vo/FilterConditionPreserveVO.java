package com.quantchi.knowledge.ai.vo;

import com.alibaba.fastjson.JSONObject;
import com.quantchi.knowledge.center.bean.entity.FilterConditionPreserve;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 筛选记录保存表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-21
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "筛选记录展示类")
public class FilterConditionPreserveVO {

    @ApiModelProperty(value = "筛选记录id")
    private Long preserveId;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("保存类型")
    private String preserveType;

    @ApiModelProperty("筛选类型，1普通筛选/2高级筛选/3头条关注")
    private Integer filterType;

    @ApiModelProperty("保存数据")
    private JSONObject preserveData;

    public static FilterConditionPreserveVO toVO(final FilterConditionPreserve filterConditionPreserve) {
        final FilterConditionPreserveVO vo = new FilterConditionPreserveVO();
        if (filterConditionPreserve != null) {
            BeanUtils.copyProperties(filterConditionPreserve, vo);
            vo.setPreserveId(filterConditionPreserve.getId());
        }
        return vo;
    }

}

package com.quantchi.knowledge.ai.vo;

import com.quantchi.knowledge.center.util.ElasticsearchBuilder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.quantchi.knowledge.center.util.ElasticsearchBuilder.LABEL_BEFORE;
import static com.quantchi.knowledge.center.util.ElasticsearchBuilder.LABEL_REAR;

/**
 * <AUTHOR>
 * @date 2023/10/11 17:41
 */
@Data
public class SuggestReturnVO {

    @ApiModelProperty("原数据")
    private String suggest;

    @ApiModelProperty("高亮后的推荐词")
    private String highlightSuggest;

    public static SuggestReturnVO toVO(final String suggest, final String keyword) {
        final SuggestReturnVO vo = new SuggestReturnVO();
        vo.setSuggest(suggest);
        vo.setHighlightSuggest(ElasticsearchBuilder.replacementInfo(suggest, keyword, LABEL_BEFORE, LABEL_REAR));
        return vo;
    }

}

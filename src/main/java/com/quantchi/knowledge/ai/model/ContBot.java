package com.quantchi.knowledge.ai.model;

import com.quantchi.knowledge.ai.entity.chatmodel.ChatBaseRequest;
import com.quantchi.knowledge.ai.entity.chatmodel.DpChatQueryRequest;
import com.quantchi.knowledge.ai.entity.response.ChatResponse;
import com.quantchi.knowledge.ai.entity.response.DpChatResponse;
import reactor.core.publisher.Flux;

/**
 * @date 2023/7/20
 */
public interface ContBot<T extends ChatBaseRequest> {

    Flux<ChatResponse> chatContOfStreamForPatent(String content, String msgUid, String patentId);

    Flux<ChatResponse> chatContOfStream(String content, String msgUid);

    Flux<DpChatResponse> chatWithThink(final DpChatQueryRequest chatQueryRequest);

}

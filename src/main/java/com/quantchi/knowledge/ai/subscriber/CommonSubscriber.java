package com.quantchi.knowledge.ai.subscriber;

import com.quantchi.knowledge.ai.common.ErrorCode;
import com.quantchi.knowledge.ai.common.WenXinUtils;
import com.quantchi.knowledge.ai.entity.Message;
import com.quantchi.knowledge.ai.entity.response.ChatResponse;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Subscriber;
import org.reactivestreams.Subscription;
import reactor.core.Disposable;
import reactor.core.publisher.FluxSink;

import java.util.Queue;
import java.util.StringJoiner;

import static com.quantchi.knowledge.ai.common.WenXinUtils.buildAssistantMessage;

/**
 * @date 2023/7/20
 */
@Slf4j
public class CommonSubscriber implements Subscriber<ChatResponse>, Disposable {

    private final FluxSink<ChatResponse> emitter;
    private final Queue<Message> messagesHistory;
    private final StringJoiner joiner = new StringJoiner("");
    private Subscription subscription;
    private final Long userId;
    private final String msgUid;

    public CommonSubscriber(final FluxSink<ChatResponse> emitter, final Queue<Message> messagesHistory) {
        this.emitter = emitter;
        this.messagesHistory = messagesHistory;
        this.userId = null;
        this.msgUid = null;
    }

    public CommonSubscriber(final FluxSink<ChatResponse> emitter, final Queue<Message> messagesHistory,
                           final Long userId, final String msgUid) {
        this.emitter = emitter;
        this.messagesHistory = messagesHistory;
        this.userId = userId;
        this.msgUid = msgUid;
    }

    @Override
    public void onSubscribe(final Subscription subscription) {
        this.subscription = subscription;
        subscription.request(50);
        log.info("onSubscribe");
    }

    @Override
    public void onNext(final ChatResponse response) {
        if (isDisposed()) {
            return;
        }
        if (response == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "ChatResponse is null !");
        }
        if (response.getContent() != null) {
            joiner.add(response.getContent());
        }
        subscription.request(50);
        emitter.next(response);
    }

    @Override
    public void onError(final Throwable throwable) {
        if (isDisposed()) {
            return;
        }

        log.info("onError");
        emitter.error(throwable);
    }

    @Override
    public void onComplete() {
        if (isDisposed()) {
            return;
        }

        log.info("onComplete");
        final String result = joiner.toString();

        final Message message = buildAssistantMessage(result, null);

        // 如果有userId和msgUid，设置到消息中以确保能保存到Redis
        if (userId != null && msgUid != null) {
            message.setUserId(userId);
            message.setMsgUid(msgUid);
            message.setTimestamp(System.currentTimeMillis());
            log.info("=== CommonSubscriber === AI回复保存，msgUid: {}, userId: {}, 内容长度: {}",
                    msgUid, userId, result.length());
        }

        WenXinUtils.offerMessage(messagesHistory, message);
        emitter.complete();
    }

    @Override
    public void dispose() {
        log.info("dispose");
        subscription.cancel();
    }

    @Override
    public boolean isDisposed() {
        return Disposable.super.isDisposed();
    }
}
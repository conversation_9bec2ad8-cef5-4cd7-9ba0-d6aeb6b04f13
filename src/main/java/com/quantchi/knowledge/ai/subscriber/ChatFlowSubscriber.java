package com.quantchi.knowledge.ai.subscriber;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.quantchi.knowledge.ai.common.ErrorCode;
import com.quantchi.knowledge.ai.entity.Message;
import com.quantchi.knowledge.ai.entity.response.DpChatResponse;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Subscriber;
import org.reactivestreams.Subscription;
import reactor.core.Disposable;
import reactor.core.publisher.FluxSink;

import java.util.Map;
import java.util.Queue;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;

import static com.quantchi.knowledge.ai.common.WenXinUtils.buildAssistantMessage;
import static com.quantchi.knowledge.ai.common.WenXinUtils.offerMessage;

/**
 * 处理工作流API响应并保存历史记录
 */
@Slf4j
public class ChatFlowSubscriber implements Subscriber<DpChatResponse>, Disposable {
    
    public static final Map<String, String> CONVERSATION_ID_MAP = new ConcurrentHashMap<>();
    
    private final FluxSink<DpChatResponse> emitter;
    private final Queue<Message> messagesHistory;
    private final String msgUid;
    private final Long userId;
    private final String expertKey;
    private Subscription subscription;
    private DpChatResponse finalContentResponse = null; // 最终的content响应
    private boolean hasCompletedReasoningOutput = false; // 标记是否已经完成了思考过程的输出
    private boolean hasOutputStatus4 = false; // 标记是否已经输出过status为4的数据流
    
    // 累积所有AI回答内容
    private final StringJoiner contentJoiner = new StringJoiner("");
    private final StringJoiner reasoningJoiner = new StringJoiner("");
    
    // 标记是否已经保存过历史记录，避免重复保存
    private volatile boolean hasHistorySaved = false;

    public ChatFlowSubscriber(final FluxSink<DpChatResponse> emitter,
                              final Queue<Message> messagesHistory,
                              final String msgUid, final Long userId,
                              final String expertKey) {
        this.emitter = emitter;
        this.messagesHistory = messagesHistory;
        this.msgUid = msgUid;
        this.userId = userId;
        this.expertKey = expertKey;
    }

    @Override
    public void onSubscribe(final Subscription subscription) {
        this.subscription = subscription;
        subscription.request(Long.MAX_VALUE);
        log.info("ChatFlowSubscriber onSubscribe");
    }

    @Override
    public void onNext(final DpChatResponse response) {
        if (isDisposed()) {
            return;
        }
        if (response == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "ChatResponse is null !");
        }

        // 处理 referContent
        final String referContent = response.getReferContent();
        if (CharSequenceUtil.isNotEmpty(referContent)) {
            try {
                // 清理 referContent 并解析为 JSONArray
                final String cleanedJsonString = referContent.replace("\n", "")
                        .replace("\\\"", "'");
                JSONArray jsonArray = JSON.parseArray(cleanedJsonString);
                response.setReferContentJson(jsonArray);
            } catch (final Exception e) {
                log.error("无法解析JSON数据, {}", referContent);
            }
        }

        // 跟踪conversationId
        if (response.getConversationId() != null) {
            CONVERSATION_ID_MAP.put(msgUid, response.getConversationId());
        }

        // 根据状态处理不同类型的响应
        Integer status = response.getStatus();

        if (status != null) {
            if (status == 3) { // 思考状态
                
                // 判断该reasoningContent是否已输出过
                String reasoningContent = response.getReasoningContent();
                if (reasoningContent != null && !reasoningContent.isEmpty()) {
                    // 累积思考内容
                    reasoningJoiner.add(reasoningContent);
                    
                    // 如果思考内容以</think>结尾，标记思考过程已经完成
                    if (reasoningContent.endsWith("</think>")) {
                        hasCompletedReasoningOutput = true;
                        log.info("思考过程已完成输出");
                    }
                }

                // 发送思考状态消息
                emitter.next(response);

            } else if (status == 4) { // 回答状态
                // 累积AI回答内容
                if (response.getContent() != null) {
                    contentJoiner.add(response.getContent());
                }
                
                // 如果这是包含最终处理结果的workflow_finished事件，仅保存不发送
                // 最终的处理结果会在onComplete中一次性发送
                if (response.getIsEnd() != null && response.getIsEnd()) {
                    finalContentResponse = response;
                    log.info("Received final processed content: {}",
                            response.getContent() != null ? response.getContent() : "<null>");
                    // 不立即发送，等待所有数据处理完成
                } else {
                    // 其他中间处理的status=4消息也需要流式输出
                    // 标记已经输出过status为4的数据流
                    hasOutputStatus4 = true;
                    emitter.next(response);
                }
            } else {
                // 其他状态直接发送
                emitter.next(response);
            }
        } else {
            // 没有状态的响应直接发送
            emitter.next(response);
        }

        if (subscription != null) {
            subscription.request(1);
        }
    }

    @Override
    public void onError(final Throwable throwable) {
        if (isDisposed()) {
            return;
        }

        log.info("ChatFlowSubscriber onError");
        
        // 获取累积的内容，即使在错误情况下也尝试保存已经收到的内容
        saveHistory();

        // 构建并发送最后一个 chatResponse
        final DpChatResponse lastResponse = new DpChatResponse();
        lastResponse.setRole("assistant");
        lastResponse.setIsEnd(true);
        lastResponse.setStatus(4);
        emitter.next(lastResponse); // 通过 emitter 发送最后一个响应
        
        emitter.error(throwable);
    }

    @Override
    public void onComplete() {
        if (isDisposed() || hasHistorySaved) {
            return;
        }

        log.info("ChatFlowSubscriber onComplete, hasCompletedReasoningOutput={}", hasCompletedReasoningOutput);

        // 获取完整的累积内容
        final String fullContent = contentJoiner.toString();
        final String fullReasoning = reasoningJoiner.toString();

        // 发送最终响应
        final DpChatResponse finalResponse = new DpChatResponse();
        finalResponse.setRole("assistant");
        finalResponse.setStatus(4);
        finalResponse.setIsEnd(true);
        finalResponse.setContent("");
        finalResponse.setReasoningContent(null); // 确保不包含思考内容
        finalResponse.setConversation_id(CONVERSATION_ID_MAP.get(msgUid));
        emitter.next(finalResponse);

        // 保存完整消息到历史记录
        try {
            final Message message = buildAssistantMessage(fullContent, fullReasoning);
            message.setMsgUid(msgUid);
            message.setUserId(userId);
            message.setExpertKey(expertKey);
            message.setTimestamp(System.currentTimeMillis());

            // 确保消息字段完整
            if (message.getUserId() != null && message.getMsgUid() != null) {
                offerMessage(messagesHistory, message);
                log.info("流式完成时保存AI回复历史记录成功，msgUid: {}, userId: {}", msgUid, userId);
            } else {
                log.error("流式完成时消息字段不完整，userId: {}, msgUid: {}", message.getUserId(), message.getMsgUid());
            }
        } catch (Exception e) {
            log.error("流式完成时保存AI回复历史记录失败，msgUid: {}, userId: {}", msgUid, userId, e);
        }
        // 标记已保存历史记录
        hasHistorySaved = true;

        emitter.complete();
    }

    @Override
    public void dispose() {
        log.info("ChatFlowSubscriber dispose");
        // 获取累积的内容，即使在错误情况下也尝试保存已经收到的内容
        saveHistory();
        if (subscription != null) {
            subscription.cancel();
        }
    }

    private void saveHistory() {
        if (hasHistorySaved) {
            log.info("历史记录已保存，跳过重复保存");
            return;
        }

        synchronized (this) {
            if (hasHistorySaved) {
                return;
            }

            final String fullContent = contentJoiner.toString();
            final String fullReasoning = reasoningJoiner.toString();

            if (CharSequenceUtil.isNotEmpty(fullContent) || CharSequenceUtil.isNotEmpty(fullReasoning)) {
                try {
                    // 保存已累积的内容到历史记录
                    final Message message = buildAssistantMessage(fullContent, fullReasoning);
                    message.setMsgUid(msgUid);
                    message.setUserId(userId);
                    message.setExpertKey(expertKey);
                    message.setTimestamp(System.currentTimeMillis());

                    // 确保消息字段完整，避免保存失败
                    if (message.getUserId() == null || message.getMsgUid() == null) {
                        log.error("消息字段不完整，userId: {}, msgUid: {}", message.getUserId(), message.getMsgUid());
                        return;
                    }

                    offerMessage(messagesHistory, message);

                    // 标记已保存历史记录
                    hasHistorySaved = true;
                    log.info("AI回复历史记录已保存，msgUid: {}, userId: {}, fullContent长度:{}, fullReasoning长度:{}",
                            msgUid, userId, fullContent.length(), fullReasoning.length());
                } catch (Exception e) {
                    log.error("保存AI回复历史记录失败，msgUid: {}, userId: {}", msgUid, userId, e);
                    // 即使保存失败也标记为已保存，避免重复尝试
                    hasHistorySaved = true;
                }
            } else {
                log.warn("AI回复内容为空，不保存历史记录，msgUid: {}, userId: {}", msgUid, userId);
                hasHistorySaved = true;
            }
        }
    }

    @Override
    public boolean isDisposed() {
        return Disposable.super.isDisposed();
    }
    
    /**
     * 获取会话ID映射
     * 
     * @return 会话ID映射
     */
    public static Map<String, String> getConversationIdMap() {
        return CONVERSATION_ID_MAP;
    }
}

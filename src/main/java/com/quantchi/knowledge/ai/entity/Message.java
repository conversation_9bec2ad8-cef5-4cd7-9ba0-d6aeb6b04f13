package com.quantchi.knowledge.ai.entity;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.knowledge.ai.common.RoleEnum;
import com.quantchi.knowledge.center.bean.vo.SysFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Message {

    @ApiModelProperty(value = "角色 user: 表示用户 assistant: 表示对话助手")
    private RoleEnum role;

    @ApiModelProperty(value = "对话内容，不能为空")
    private String content;

    @ApiModelProperty(value = "推理内容")
    @JsonProperty("reasoning_content")
    private String reasoningContent;

    @ApiModelProperty("参考文献的Json数据")
    private JSONArray referContentJson;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("记录id")
    private String msgUid;

    @ApiModelProperty("时间戳")
    private Long timestamp;

    @ApiModelProperty("文件列表")
    private List<SysFileVO> fileList;

    @ApiModelProperty("智能体类型")
    private String type;

    @ApiModelProperty("专家智能体key")
    private String expertKey;

    public Message(final RoleEnum role, final String content, final String reasoningContent) {
        this.role = role;
        this.content = content;
        this.reasoningContent = reasoningContent;
    }

}

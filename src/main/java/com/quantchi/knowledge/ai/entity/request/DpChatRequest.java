package com.quantchi.knowledge.ai.entity.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.knowledge.ai.entity.BaseRequest;
import com.quantchi.knowledge.ai.entity.Message;
import com.quantchi.knowledge.ai.entity.UserFile;
import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.Queue;

/**
 * @date 2023/7/20
 * <p>
 * ContBot 模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DpChatRequest extends BaseRequest {

    @JsonProperty("model")
    private String model;

    @JsonProperty("knowledge_db_ids")
    private List<String> knowledge_db_ids = Collections.singletonList("__default__");

    @JsonProperty("user_files")
    private List<UserFile> user_files;

    @JsonProperty("expert_key")
    private String expert_key;

    public static DpChatRequestBuilder builder() {
        return new DpChatRequestBuilder();
    }

    public static class DpChatRequestBuilder extends BaseRequestBuilder {
        private String model;
        private String userId;
        private Queue<Message> messages;
        private Boolean stream;
        private List<UserFile> user_files;

        public DpChatRequestBuilder model(final String model) {
            this.model = model;
            return this;
        }

        @Override
        public DpChatRequestBuilder userId(final String userId) {
            this.userId = userId;
            return this;
        }

        @Override
        public DpChatRequestBuilder messages(final Queue<Message> messages) {
            this.messages = messages;
            return this;
        }

        @Override
        public DpChatRequestBuilder stream(final Boolean stream) {
            this.stream = stream;
            return this;
        }

        public DpChatRequestBuilder userFiles(final List<UserFile> userFiles) {
            this.user_files = userFiles;
            return this;
        }

        @Override
        public DpChatRequest build() {
            final DpChatRequest ernieRequest = new DpChatRequest();
            ernieRequest.setModel(model);
            ernieRequest.setUserId(userId);
            ernieRequest.setMessages(messages);
            ernieRequest.setStream(stream);
            ernieRequest.setUser_files(user_files);
            return ernieRequest;
        }
    }

}

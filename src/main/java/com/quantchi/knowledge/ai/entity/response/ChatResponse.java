package com.quantchi.knowledge.ai.entity.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.knowledge.ai.entity.Usage;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @date 2023/7/20
 * <p>
 * ContBot 模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponse implements Serializable {

    @JsonProperty("choices")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Choice> choices;

    @JsonProperty("task_id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String taskId;

    @JsonProperty("request_id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String requestId;

    private String model;
    /**
     * 本轮对话的id
     */
    @JsonProperty("id")
    private String id;
    /**
     * 回包类型
     * chat.completion：多轮对话返回
     */
    @JsonProperty("object")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String object;
    /**
     * 时间戳
     */
    @JsonProperty("created")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer created;
    /**
     * 表示当前子句的序号。只有在流式接口模式下会返回该字段
     */
    @JsonProperty("sentence_id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sentenceId;
    /**
     * 表示当前子句是否是最后一句。只有在流式接口模式下会返回该字段
     */
    @JsonProperty("is_end")
    private Boolean isEnd = false;
    /**
     * 当前生成的结果是否被截断
     */
    @JsonProperty("is_truncated")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isTruncated;
    /**
     * 对话返回结果
     */
    @JsonProperty("content")
    private String content = "";
    @ApiModelProperty("图片地址")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String imgUrl;
    @ApiModelProperty("图片链接集合")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> imgs;
    /**
     * 表示用户输入是否存在安全，是否关闭当前会话，清理历史回话信息
     * true：是，表示用户输入存在安全风险，建议关闭当前会话，清理历史会话信息
     * false：否，表示用户输入无安全风险
     */
    @JsonProperty("need_clear_history")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean needClearHistory;
    /**
     * token统计信息，token数 = 汉字数+单词数*1.3 （仅为估算逻辑）
     */
    @JsonProperty("usage")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Usage usage;
    /**
     * 当need_clear_history为true时，此字段会告知第几轮对话有敏感信息，如果是当前问题，ban_round=-1
     */
    @JsonProperty("ban_round")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Usage banRound;
    /**
     * 错误代码，正常为 null
     */
    @JsonProperty("error_code")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer errorCode;
    /**
     * 错误信息，正常为 null
     */
    @JsonProperty("error_msg")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String errorMsg;

    public ChatResponse(final Boolean isEnd, final List<String> imgs) {
        this.isEnd = isEnd;
        this.imgs = imgs;
    }

}

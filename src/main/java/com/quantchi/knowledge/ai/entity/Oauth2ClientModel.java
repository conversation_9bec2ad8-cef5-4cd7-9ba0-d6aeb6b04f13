package com.quantchi.knowledge.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.knowledge.center.bean.entity.BaseTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * oauth2客户端信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("oauth2_client_model")
@ApiModel(value = "Oauth2ClientModel对象", description = "oauth2客户端信息")
public class Oauth2ClientModel extends BaseTime implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("客户端id")
    @TableId(value = "client_id", type = IdType.ASSIGN_ID)
    private Long clientId;

    @ApiModelProperty("客户端秘钥")
    private String clientSecret;

    @ApiModelProperty("openId")
    private String openId;

    @ApiModelProperty("应用允许授权的所有url，多个用逗号隔开")
    private String allowUrl;

    @ApiModelProperty("应用签约的所有权限，多个用逗号隔开")
    private String contractScope;

}

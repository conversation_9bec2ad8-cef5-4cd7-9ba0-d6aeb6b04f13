package com.quantchi.knowledge.ai.entity.request;

import com.quantchi.knowledge.ai.bo.PageBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/9/15 17:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportPromptPageRequest extends PageBO {

    @ApiModelProperty("种类，如政策、画像、报告模板市场等")
    private String category;

    @ApiModelProperty("1写报告灵感/2报告模版市场")
    private Integer subType;

    @ApiModelProperty("关键词")
    private String keyword;

}

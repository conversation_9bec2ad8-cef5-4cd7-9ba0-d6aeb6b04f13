package com.quantchi.knowledge.ai.entity.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.knowledge.ai.entity.Message;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/9 下午2:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Choice {

    @JsonProperty("finish_reason")
    private String finishReason;

    @JsonProperty("index")
    private Long index;

    @JsonProperty("delta")
    private Delta delta;

    @JsonProperty("message")
    private Message message;
}

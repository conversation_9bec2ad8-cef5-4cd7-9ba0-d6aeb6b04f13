package com.quantchi.knowledge.ai.entity.chatmodel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChatQueryRequest extends SingleChatQueryRequest {

    @ApiModelProperty("会话id")
    private String msgUid;

    @ApiModelProperty("专利id")
    private String patentId;

}

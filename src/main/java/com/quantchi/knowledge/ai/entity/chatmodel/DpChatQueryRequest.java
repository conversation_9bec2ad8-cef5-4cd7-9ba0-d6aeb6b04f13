package com.quantchi.knowledge.ai.entity.chatmodel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DpChatQueryRequest extends SingleChatQueryRequest {

    @ApiModelProperty("会话id")
    private String msgUid;

    @ApiModelProperty("模型，reasoning推理模型或者default对话模型")
    private String model = "reasoning";

    @ApiModelProperty("总的文件ID列表")
    private List<Long> fileIds;

    @ApiModelProperty("当前聊天上传的文件id列表")
    private List<Long> currentFileIds;
    
    @ApiModelProperty("是否重新生成回答，true表示重新生成最后一次回答")
    private Boolean regenerate = false;
    
    @ApiModelProperty("是否编辑问题，true表示编辑最后一次提问")
    private Boolean editQuestion = false;

    @ApiModelProperty("会话id")
    private String conversationId;
    
    @ApiModelProperty("智能体类型")
    private String expertKey;

}

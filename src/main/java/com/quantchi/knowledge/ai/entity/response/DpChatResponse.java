package com.quantchi.knowledge.ai.entity.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @date 2023/7/20
 * <p>
 * ContBot 模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DpChatResponse implements Serializable {

    @JsonProperty("role")
    @ApiModelProperty("角色")
    private String role;

    @JsonProperty("content")
    @ApiModelProperty("正文内容")
    private String content = "";

    @JsonProperty("reasoning_content")
    @ApiModelProperty("推理过程")
    private String reasoningContent;

    @JsonProperty("refer_content")
    @ApiModelProperty("参考文献")
    private String referContent;

    @JsonProperty("referContentJson")
    @ApiModelProperty("参考文献的Json数据")
    private Object referContentJson; // 使用Object类型支持JSONObject和JSONArray

    @ApiModelProperty("状态，1理解问题/2查找信息/3深度思考/4生成回答")
    private Integer status;

    private Boolean isEnd = false;
    
    @JsonProperty("conversation_id")
    @ApiModelProperty("对话ID")
    private String conversationId;
    
    @ApiModelProperty("问答类型")
    private String type;

    @ApiModelProperty("智能体类型")
    private String  expertKey;
    
    /**
     * 设置对话ID（兼容旧版本API）
     */
    public void setConversation_id(String conversationId) {
        this.conversationId = conversationId;
    }

}

package com.quantchi.knowledge.ai.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户上传的文件信息
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@Data
@NoArgsConstructor
public class UserFile {

    /**
     * 文件名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 文件内容
     */
    @JsonProperty("content")
    private String content;

    @ApiModelProperty("文件链接")
    @JsonProperty("url")
    private String url;

    /**
     * 上传时间
     */
    @JsonProperty("uploaded_at")
    private String uploadedAt;

    public UserFile(final String name, final String content, final String url, final String uploadedAt) {
        this.name = name;
        this.content = content;
        this.url = url;
        this.uploadedAt = uploadedAt;
    }
}

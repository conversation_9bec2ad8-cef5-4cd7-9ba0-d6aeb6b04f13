#!/bin/bash

# ========================================
# 动态表索引批量创建脚本
# 使用说明：./scripts/create_indexes.sh [选项]
# ========================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
API_BASE_URL="http://localhost:8612/api/index-management"
TIMEOUT=300  # 5分钟超时

# 显示帮助信息
show_help() {
    echo -e "${BLUE}动态表索引批量创建工具${NC}"
    echo ""
    echo "使用方法："
    echo "  $0 [选项]"
    echo ""
    echo "选项："
    echo "  -h, --help              显示此帮助信息"
    echo "  -u, --url URL          指定API基础URL (默认: $API_BASE_URL)"
    echo "  -l, --list             查看所有产业链列表"
    echo "  -s, --statistics       查看表统计信息"
    echo "  -a, --all              创建所有表的索引"
    echo "  -c, --company          仅创建 company_node 表索引"
    echo "  -p, --patent           仅创建 patent_node 表索引"
    echo "  -d, --drop CHAIN_ID    删除指定产业链的索引"
    echo "  --chain CHAIN_ID       为指定产业链创建索引"
    echo ""
    echo "示例："
    echo "  $0 --list                          # 查看产业链列表"
    echo "  $0 --all                           # 创建所有索引"
    echo "  $0 --company                       # 仅创建 company_node 索引"
    echo "  $0 --chain industry_ai             # 为人工智能产业链创建索引"
    echo "  $0 --drop industry_ai              # 删除人工智能产业链索引"
    echo ""
}

# 检查curl是否可用
check_curl() {
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}错误: curl 命令未找到，请先安装 curl${NC}"
        exit 1
    fi
}

# 检查jq是否可用（可选）
check_jq() {
    if command -v jq &> /dev/null; then
        return 0
    else
        echo -e "${YELLOW}提示: 建议安装 jq 以获得更好的JSON格式化输出${NC}"
        return 1
    fi
}

# 发送API请求
api_request() {
    local method=$1
    local endpoint=$2
    local url="${API_BASE_URL}${endpoint}"
    
    echo -e "${BLUE}发送请求: ${method} ${url}${NC}"
    
    if check_jq; then
        curl -s -X "${method}" "${url}" \
            -H "Content-Type: application/json" \
            --connect-timeout 10 \
            --max-time ${TIMEOUT} | jq '.'
    else
        curl -s -X "${method}" "${url}" \
            -H "Content-Type: application/json" \
            --connect-timeout 10 \
            --max-time ${TIMEOUT}
    fi
    
    local exit_code=${PIPESTATUS[0]}
    if [ $exit_code -ne 0 ]; then
        echo -e "${RED}请求失败，退出码: $exit_code${NC}"
        return 1
    fi
}

# 查看产业链列表
list_chains() {
    echo -e "${GREEN}📋 获取产业链列表...${NC}"
    api_request "GET" "/chain-list"
}

# 查看表统计信息
show_statistics() {
    echo -e "${GREEN}📊 获取表统计信息...${NC}"
    api_request "GET" "/table-statistics"
}

# 创建所有索引
create_all_indexes() {
    echo -e "${GREEN}🚀 开始创建所有表的索引...${NC}"
    echo -e "${YELLOW}注意: 这可能需要几分钟时间，请耐心等待${NC}"
    api_request "POST" "/create-all-indexes"
}

# 创建 company_node 索引
create_company_indexes() {
    echo -e "${GREEN}🚀 开始创建 company_node 表索引...${NC}"
    api_request "POST" "/create-company-node-indexes"
}

# 创建 patent_node 索引
create_patent_indexes() {
    echo -e "${GREEN}🚀 开始创建 patent_node 表索引...${NC}"
    api_request "POST" "/create-patent-node-indexes"
}

# 为指定产业链创建索引
create_chain_indexes() {
    local chain_id=$1
    if [ -z "$chain_id" ]; then
        echo -e "${RED}错误: 请指定产业链ID${NC}"
        return 1
    fi
    
    echo -e "${GREEN}🚀 为产业链 ${chain_id} 创建索引...${NC}"
    api_request "POST" "/create-indexes-for-chain/${chain_id}"
}

# 删除指定产业链的索引
drop_chain_indexes() {
    local chain_id=$1
    if [ -z "$chain_id" ]; then
        echo -e "${RED}错误: 请指定产业链ID${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}⚠️  确认要删除产业链 ${chain_id} 的所有索引吗? (y/N)${NC}"
    read -r confirmation
    if [[ $confirmation =~ ^[Yy]$ ]]; then
        echo -e "${GREEN}🗑️  删除产业链 ${chain_id} 的索引...${NC}"
        api_request "DELETE" "/drop-indexes/${chain_id}"
    else
        echo -e "${BLUE}操作已取消${NC}"
    fi
}

# 主函数
main() {
    check_curl
    
    # 如果没有参数，显示帮助
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--url)
                API_BASE_URL="$2"
                shift 2
                ;;
            -l|--list)
                list_chains
                exit 0
                ;;
            -s|--statistics)
                show_statistics
                exit 0
                ;;
            -a|--all)
                create_all_indexes
                exit 0
                ;;
            -c|--company)
                create_company_indexes
                exit 0
                ;;
            -p|--patent)
                create_patent_indexes
                exit 0
                ;;
            --chain)
                create_chain_indexes "$2"
                exit 0
                ;;
            -d|--drop)
                drop_chain_indexes "$2"
                exit 0
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
}

# 执行主函数
main "$@"

-- ========================================
-- 测试索引创建修复脚本
-- 用于验证兼容性修复是否有效
-- ========================================

-- 1. 检查MySQL版本
SELECT VERSION() as mysql_version;

-- 2. 检查是否支持 CREATE INDEX IF NOT EXISTS 语法
-- 这个查询会显示MySQL版本信息，帮助判断是否支持该语法
SELECT 
    VERSION() as version,
    CASE 
        WHEN VERSION() >= '5.7.0' THEN '支持 IF NOT EXISTS'
        ELSE '不支持 IF NOT EXISTS，需要使用兼容性方案'
    END as if_not_exists_support;

-- 3. 查看柔性电子产业链的表是否存在
SELECT 
    table_name,
    table_rows,
    data_length,
    index_length
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('industry_fpc_company_node', 'industry_fpc_patent_node');

-- 4. 检查柔性电子产业链的现有索引
SELECT 
    table_name,
    index_name,
    column_name,
    seq_in_index,
    non_unique
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'industry_fpc_company_node'
ORDER BY table_name, index_name, seq_in_index;

-- 5. 手动测试兼容性索引创建（针对柔性电子产业链）
-- 先检查索引是否存在
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'industry_fpc_company_node' 
    AND index_name = 'idx_industry_fpc_company_node_region_valid'
);

-- 如果索引不存在，则创建
SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_industry_fpc_company_node_region_valid ON industry_fpc_company_node (is_valid, province, city, area)',
    'SELECT "索引已存在" as result'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 验证索引是否创建成功
SELECT 
    'idx_industry_fpc_company_node_region_valid' as expected_index,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 索引存在'
        ELSE '❌ 索引不存在'
    END as status
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'industry_fpc_company_node' 
AND index_name = 'idx_industry_fpc_company_node_region_valid';

-- 7. 查看所有产业链表的存在情况
SELECT 
    CONCAT(ic.name_en, '_company_node') as table_name,
    ic.name as chain_name,
    CASE 
        WHEN t.table_name IS NOT NULL THEN '✅ 存在'
        ELSE '❌ 不存在'
    END as table_exists,
    COALESCE(t.table_rows, 0) as row_count
FROM industry_chain ic
LEFT JOIN information_schema.tables t ON (
    t.table_schema = DATABASE() 
    AND t.table_name = CONCAT(ic.name_en, '_company_node')
)
WHERE ic.is_valid = 1
ORDER BY ic.sequence;

-- 8. 生成兼容性索引创建语句（示例）
SELECT 
    CONCAT(
        '-- 产业链: ', ic.name, ' (', ic.name_en, ')', CHAR(10),
        'SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = ''', ic.name_en, '_company_node'' AND index_name = ''idx_', ic.name_en, '_company_node_region_valid'');', CHAR(10),
        'SET @sql = IF(@index_exists = 0, ''CREATE INDEX idx_', ic.name_en, '_company_node_region_valid ON ', ic.name_en, '_company_node (is_valid, province, city, area)'', ''SELECT ''''Index exists'''' as result'');', CHAR(10),
        'PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;', CHAR(10),
        CHAR(10)
    ) as compatible_sql
FROM industry_chain ic
WHERE ic.is_valid = 1 
AND EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = CONCAT(ic.name_en, '_company_node')
)
ORDER BY ic.sequence
LIMIT 5;  -- 只显示前5个作为示例

-- 9. 检查当前数据库的所有索引统计
SELECT 
    table_name,
    COUNT(*) as index_count,
    GROUP_CONCAT(DISTINCT index_name ORDER BY index_name) as index_names
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name LIKE '%_company_node'
GROUP BY table_name
ORDER BY table_name;

-- 10. 性能测试查询（验证索引效果）
-- 注意：只有在表存在且有数据时才执行
-- EXPLAIN SELECT COUNT(*) FROM industry_fpc_company_node WHERE is_valid = 1 AND province = '广东省';

-- ========================================
-- 动态表索引批量创建脚本
-- 为所有产业链的 company_node 和 patent_node 表创建索引
-- ========================================

-- 使用说明：
-- 1. 首先执行查询语句获取所有产业链ID
-- 2. 将查询结果复制到下面的索引创建语句中
-- 3. 执行生成的索引创建语句

-- ========================================
-- 第一步：查询所有产业链ID
-- ========================================

-- 查询所有有效的产业链ID（nameEn字段作为chainId）
SELECT 
    name_en as chain_id,
    name as chain_name,
    label,
    CONCAT('-- 产业链: ', name, ' (', name_en, ')') as comment
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

-- ========================================
-- 第二步：为每个产业链生成索引创建语句
-- ========================================

-- 生成 company_node 表索引创建语句
SELECT 
    CONCAT(
        '-- 产业链: ', name, ' (', name_en, ')', CHAR(10),
        '-- company_node 表索引', CHAR(10),
        'CREATE INDEX idx_', name_en, '_company_node_region_valid ON ', name_en, '_company_node (is_valid, province, city, area);', CHAR(10),
        'CREATE INDEX idx_', name_en, '_company_node_node_region ON ', name_en, '_company_node (node_id, is_valid, province, city, area);', CHAR(10),
        'CREATE INDEX idx_', name_en, '_company_node_cid_valid ON ', name_en, '_company_node (cid, is_valid);', CHAR(10),
        'CREATE INDEX idx_', name_en, '_company_node_establish_date ON ', name_en, '_company_node (establish_date, is_valid);', CHAR(10)
    ) as sql_statements
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

-- 生成 patent_node 表索引创建语句
SELECT 
    CONCAT(
        '-- patent_node 表索引', CHAR(10),
        'CREATE INDEX idx_', name_en, '_patent_node_region_valid ON ', name_en, '_patent_node (is_valid, province, city);', CHAR(10),
        'CREATE INDEX idx_', name_en, '_patent_node_type_date ON ', name_en, '_patent_node (patent_type, public_date, is_valid);', CHAR(10),
        'CREATE INDEX idx_', name_en, '_patent_node_node_valid ON ', name_en, '_patent_node (node_id, is_valid);', CHAR(10),
        'CREATE INDEX idx_', name_en, '_patent_node_ipc_valid ON ', name_en, '_patent_node (main_ipc, is_valid);', CHAR(10),
        CHAR(10)
    ) as sql_statements
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

-- ========================================
-- 第三步：检查表是否存在的语句
-- ========================================

-- 检查 company_node 表是否存在
SELECT 
    CONCAT(
        'SELECT COUNT(*) as table_exists FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ''', 
        name_en, '_company_node'', ''';'
    ) as check_sql
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

-- 检查 patent_node 表是否存在
SELECT 
    CONCAT(
        'SELECT COUNT(*) as table_exists FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ''', 
        name_en, '_patent_node'', ''';'
    ) as check_sql
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

-- ========================================
-- 第四步：检查索引是否已存在的语句
-- ========================================

-- 检查 company_node 表的索引是否存在
SELECT 
    CONCAT(
        'SELECT COUNT(*) as index_exists FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = ''', 
        name_en, '_company_node'' AND index_name = ''idx_', name_en, '_company_node_region_valid'';'
    ) as check_index_sql
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

-- ========================================
-- 第五步：安全的索引创建语句（带存在性检查）
-- ========================================

-- 生成安全的 company_node 索引创建语句
SELECT 
    CONCAT(
        '-- 产业链: ', name, ' (', name_en, ')', CHAR(10),
        'SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ''', name_en, '_company_node'');', CHAR(10),
        'SET @sql = IF(@table_exists > 0, ''CREATE INDEX IF NOT EXISTS idx_', name_en, '_company_node_region_valid ON ', name_en, '_company_node (is_valid, province, city, area)'', ''SELECT ''''Table does not exist'''' as result'');', CHAR(10),
        'PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;', CHAR(10),
        CHAR(10)
    ) as safe_sql
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

-- ========================================
-- 第六步：批量删除索引的语句（如果需要重建）
-- ========================================

-- 生成删除 company_node 索引的语句
SELECT 
    CONCAT(
        '-- 删除产业链: ', name, ' (', name_en, ') 的索引', CHAR(10),
        'DROP INDEX IF EXISTS idx_', name_en, '_company_node_region_valid ON ', name_en, '_company_node;', CHAR(10),
        'DROP INDEX IF EXISTS idx_', name_en, '_company_node_node_region ON ', name_en, '_company_node;', CHAR(10),
        'DROP INDEX IF EXISTS idx_', name_en, '_company_node_cid_valid ON ', name_en, '_company_node;', CHAR(10),
        'DROP INDEX IF EXISTS idx_', name_en, '_company_node_establish_date ON ', name_en, '_company_node;', CHAR(10)
    ) as drop_sql
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

-- 生成删除 patent_node 索引的语句
SELECT 
    CONCAT(
        'DROP INDEX IF EXISTS idx_', name_en, '_patent_node_region_valid ON ', name_en, '_patent_node;', CHAR(10),
        'DROP INDEX IF EXISTS idx_', name_en, '_patent_node_type_date ON ', name_en, '_patent_node;', CHAR(10),
        'DROP INDEX IF EXISTS idx_', name_en, '_patent_node_node_valid ON ', name_en, '_patent_node;', CHAR(10),
        'DROP INDEX IF EXISTS idx_', name_en, '_patent_node_ipc_valid ON ', name_en, '_patent_node;', CHAR(10)
    ) as drop_sql
FROM industry_chain 
WHERE is_valid = 1 
ORDER BY sequence;

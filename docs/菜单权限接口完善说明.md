# 菜单权限接口完善说明

## 更新内容

### 🔄 **2024-07-21 更新：动态智能体菜单权限控制**

在原有的菜单权限控制基础上，进一步完善了动态智能体菜单的权限控制逻辑：

#### 问题分析
原来的 `addDynamicAgentMenus` 方法会无条件地为所有用户添加智能体菜单，没有考虑用户的菜单权限。这可能导致：
1. 用户看到无权访问的智能体菜单
2. 权限控制不一致的问题

#### 解决方案
1. **精确权限控制**：每个智能体菜单都有独立的菜单ID，在总控配置后台单独配置权限
2. **统一权限管理**：智能体菜单权限和其他菜单一样存储在 `sys_user_menu` 或 `sys_role_menu` 表中
3. **动态权限过滤**：根据用户的具体菜单权限，只显示有权限的智能体菜单

#### 具体修改

##### 修改 menuTree 方法
```java
@GetMapping("/menuTree")
@ApiOperation("获取角色可选择总菜单树")
public ResultInfo<List<Tree<Long>>> menuTree() {
    final List<SysMenu> availableMenus = getAvailableMenusForCurrentUser();
    final List<Tree<Long>> treeList = menuService.buildMenuTreeSelect(availableMenus);

    // 根据用户的菜单权限添加动态智能体菜单
    addDynamicAgentMenusWithPermissionCheck(treeList, availableMenus);

    return ResultConvert.success(treeList);
}
```

##### 核心权限检查逻辑
```java
/**
 * 添加动态智能体菜单并进行权限检查
 * 根据用户的菜单权限过滤智能体菜单，只显示用户有权限的智能体
 */
private void addDynamicAgentMenusWithPermissionCheck(final List<Tree<Long>> treeList, final List<SysMenu> availableMenus) {
    try {
        // 获取当前用户信息
        final Long userId = StpUtil.getLoginIdAsLong();
        final SysUser currentUser = sysUserMapper.selectById(userId);

        // 查询启用的智能体配置
        final List<SysAgentConfig> enabledAgents = agentConfigService.list(
                Wrappers.<SysAgentConfig>lambdaQuery()
                        .eq(SysAgentConfig::getEnabled, true)
                        .orderByAsc(SysAgentConfig::getSort)
        );

        // 查找智能问答菜单节点（ID=8）
        final Tree<Long> intelligentQANode = findNodeById(treeList, 8L);
        if (intelligentQANode == null) {
            return;
        }

        // 获取用户有权限的菜单ID集合
        final Set<Long> userMenuIds = getUserMenuIds(currentUser);

        // 为每个启用的智能体检查权限并创建菜单节点
        for (final SysAgentConfig agent : enabledAgents) {
            final Long agentMenuId = Long.valueOf(Math.abs(agent.getAgentKey().hashCode()));

            // 检查用户是否有该智能体菜单的权限
            if (currentUser.isSuperAdmin() || userMenuIds.contains(agentMenuId)) {
                final Tree<Long> agentNode = createAgentTreeNode(agent);
                if (intelligentQANode.getChildren() == null) {
                    intelligentQANode.setChildren(new ArrayList<>());
                }
                intelligentQANode.getChildren().add(agentNode);
            }
        }

    } catch (Exception e) {
        // 异常处理，不影响原有菜单的返回
    }
}
```

#### 权限控制逻辑
1. **智能体菜单ID生成**：每个智能体的菜单ID = `Math.abs(agent.getAgentKey().hashCode())`
2. **权限配置**：在总控配置后台为每个智能体菜单单独配置权限，存储在 `sys_user_menu` 或 `sys_role_menu` 表中
3. **超级管理员**：可以看到所有启用的智能体菜单
4. **普通用户**：只能看到在权限表中配置了权限的智能体菜单
5. **异常处理**：确保动态菜单添加失败不影响基础菜单的返回

#### 扩展性考虑
当前实现为未来的精细化权限控制预留了空间：
- 可以在 `SysAgentConfig` 表中添加权限相关字段
- 可以创建智能体权限关联表
- 可以基于用户角色或账号类型进行更细粒度的控制

---

## 问题描述

原有的 `/back/system/role/menuTree` 接口只是获取了系统里所有的菜单权限，没有根据当前账号的菜单权限进行过滤。实际上，获取菜单权限的时候，需要根据当前账号的菜单权限选择展示哪些菜单：

- 如果是主账号：需要去 `sys_user_menu` 获取主账号对应的所有菜单
- 如果是子账号：需要去 `sys_role_menu` 获取子账号对应的角色的所有菜单

## 解决方案

### 1. 修改的文件
- `src/main/java/com/quantchi/knowledge/center/controller/SysRoleController.java`

### 2. 主要修改内容

#### 2.1 添加必要的导入
```java
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.quantchi.knowledge.center.bean.entity.SysUserMenu;
import com.quantchi.knowledge.center.bean.enums.AccountLevelEnum;
import com.quantchi.knowledge.center.bean.system.entity.SysRoleMenu;
import com.quantchi.knowledge.center.dao.mysql.SysRoleMenuMapper;
import com.quantchi.knowledge.center.dao.mysql.SysUserMapper;
import com.quantchi.knowledge.center.dao.mysql.SysUserMenuMapper;
import com.quantchi.knowledge.center.dao.mysql.SysUserRoleMapper;
import java.util.HashSet;
import java.util.Objects;
```

#### 2.2 添加依赖注入
```java
private final SysUserMapper sysUserMapper;
private final SysUserMenuMapper sysUserMenuMapper;
private final SysUserRoleMapper sysUserRoleMapper;
private final SysRoleMenuMapper sysRoleMenuMapper;
```

#### 2.3 修改 menuTree 方法
将原来直接获取所有菜单的逻辑改为根据当前用户权限获取可用菜单：
```java
@GetMapping("/menuTree")
@ApiOperation("获取角色可选择总菜单树")
public ResultInfo<List<Tree<Long>>> menuTree() {
    final List<SysMenu> availableMenus = getAvailableMenusForCurrentUser();
    final List<Tree<Long>> treeList = menuService.buildMenuTreeSelect(availableMenus);
    // 添加动态智能体菜单
    agentConfigService.addDynamicAgentMenus(treeList);
    return ResultConvert.success(treeList);
}
```

#### 2.4 新增核心方法

##### getAvailableMenusForCurrentUser()
- 获取当前登录用户信息
- 判断是否为超级管理员（返回所有菜单）
- 根据用户账号级别获取对应的菜单权限
- 过滤菜单并包含父级菜单以保证树结构完整性

##### getUserMenuIds(SysUser user)
- 主账号（AccountLevelEnum.MAIN）：从 `sys_user_menu` 表获取菜单权限
- 子账号：从 `sys_role_menu` 表通过用户角色获取菜单权限
- 特殊处理超级管理员角色（UserConstants.ADMIN_ID）

##### filterMenusWithParents()
- 确保菜单树的完整性
- 为每个有权限的菜单添加其所有父级菜单
- 避免菜单树结构断裂

##### addParentMenuIds()
- 递归添加父级菜单ID
- 确保菜单层级关系完整

### 3. 权限逻辑说明

#### 3.1 用户类型判断
- **超级管理员**：用户ID等于 `UserConstants.ADMIN_ID`（1L），返回所有菜单
- **主账号**：`accountLevel` 等于 `AccountLevelEnum.MAIN.getId()`（1）
- **子账号**：其他情况

#### 3.2 菜单权限获取
- **主账号**：
  ```sql
  SELECT menu_id FROM sys_user_menu WHERE account_id = ?
  ```
- **子账号**：
  ```sql
  SELECT rm.menu_id FROM sys_role_menu rm 
  INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id 
  WHERE ur.user_id = ?
  ```

#### 3.3 父级菜单处理
为了保证菜单树的完整性，系统会自动添加所有必要的父级菜单，即使用户没有直接权限访问这些父级菜单。

### 4. 兼容性说明

- 保持了原有接口的签名和返回格式
- 保留了动态智能体菜单的添加逻辑
- 向下兼容，不影响现有的前端调用

### 5. 安全性提升

- 根据用户实际权限过滤菜单，避免权限泄露
- 支持主账号和子账号的不同权限模型
- 保持了超级管理员的特殊权限处理

## 测试建议

1. **主账号测试**：使用主账号登录，验证只能看到 `sys_user_menu` 表中配置的菜单
2. **子账号测试**：使用子账号登录，验证只能看到其角色在 `sys_role_menu` 表中配置的菜单
3. **超级管理员测试**：使用超级管理员登录，验证能看到所有菜单
4. **菜单树完整性测试**：确认返回的菜单树结构完整，包含必要的父级菜单

## 注意事项

1. 确保数据库中 `sys_user_menu` 和 `sys_role_menu` 表的数据正确配置
2. 用户的 `account_level` 字段需要正确设置
3. 角色权限配置需要与实际业务需求匹配

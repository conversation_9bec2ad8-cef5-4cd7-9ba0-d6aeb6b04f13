# WebSocket连接使用说明

## 功能概述

WebSocket连接用于实时接收报告生成完成通知，让用户能够及时了解AI报告的生成状态变化。

## 连接信息获取

### API接口
```
GET /notification/websocket/info
```

### 请求示例
```javascript
const getWebSocketInfo = async () => {
  try {
    const response = await api.get('/notification/websocket/info');
    return response.data;
  } catch (error) {
    console.error('获取WebSocket连接信息失败', error);
    throw error;
  }
};
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "userId": 123456,
    "webSocketUrl": "wss://your-domain.com/api/ws/report-notification?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "description": "使用此URL建立WebSocket连接以接收实时通知"
  }
}
```

## URL构建逻辑

### 动态URL生成
系统根据 `file.preUrl` 配置动态生成WebSocket连接URL：

1. **协议转换**：
   - `https://` → `wss://`
   - `http://` → `ws://`
   - 无协议前缀 → 默认 `ws://`

2. **路径拼接**：
   - 基础URL + `/api/ws/report-notification`
   - 添加token参数：`?token={userToken}`

### 配置示例
```yaml
# application.yml
file:
  preUrl: https://your-domain.com
  # 或者
  preUrl: http://localhost:8612
```

对应生成的WebSocket URL：
- `wss://your-domain.com/api/ws/report-notification?token=xxx`
- `ws://localhost:8612/api/ws/report-notification?token=xxx`

## 前端连接实现

### 1. 基础连接
```javascript
class WebSocketManager {
  constructor() {
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
  }

  async connect() {
    try {
      // 获取连接信息
      const wsInfo = await getWebSocketInfo();
      
      // 建立连接
      this.ws = new WebSocket(wsInfo.webSocketUrl);
      
      this.ws.onopen = this.onOpen.bind(this);
      this.ws.onmessage = this.onMessage.bind(this);
      this.ws.onclose = this.onClose.bind(this);
      this.ws.onerror = this.onError.bind(this);
      
    } catch (error) {
      console.error('WebSocket连接失败', error);
      this.scheduleReconnect();
    }
  }

  onOpen(event) {
    console.log('WebSocket连接已建立');
    this.reconnectAttempts = 0;
  }

  onMessage(event) {
    try {
      const notification = JSON.parse(event.data);
      this.handleNotification(notification);
    } catch (error) {
      console.error('解析WebSocket消息失败', error);
    }
  }

  onClose(event) {
    console.log('WebSocket连接已关闭', event);
    if (event.code !== 1000) { // 非正常关闭
      this.scheduleReconnect();
    }
  }

  onError(error) {
    console.error('WebSocket连接错误', error);
  }

  handleNotification(notification) {
    switch (notification.type) {
      case 'REPORT_COMPLETED':
        this.handleReportCompleted(notification);
        break;
      case 'REPORT_FAILED':
        this.handleReportFailed(notification);
        break;
      default:
        console.log('收到未知类型通知', notification);
    }
  }

  handleReportCompleted(notification) {
    // 显示报告完成通知
    showNotification({
      type: 'success',
      title: '报告生成完成',
      message: `报告"${notification.data.reportTitle}"已生成完成`,
      action: {
        text: '查看报告',
        onClick: () => {
          window.open(notification.data.reportUrl, '_blank');
        }
      }
    });
  }

  scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectInterval);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, '用户主动断开连接');
      this.ws = null;
    }
  }
}
```

### 2. React Hook实现
```javascript
import { useEffect, useRef, useState } from 'react';

const useWebSocket = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const wsRef = useRef(null);
  const wsManagerRef = useRef(null);

  useEffect(() => {
    // 创建WebSocket管理器
    wsManagerRef.current = new WebSocketManager();
    
    // 重写handleNotification方法
    wsManagerRef.current.handleNotification = (notification) => {
      setNotifications(prev => [...prev, notification]);
    };
    
    // 重写onOpen方法
    const originalOnOpen = wsManagerRef.current.onOpen;
    wsManagerRef.current.onOpen = (event) => {
      originalOnOpen.call(wsManagerRef.current, event);
      setIsConnected(true);
    };
    
    // 重写onClose方法
    const originalOnClose = wsManagerRef.current.onClose;
    wsManagerRef.current.onClose = (event) => {
      originalOnClose.call(wsManagerRef.current, event);
      setIsConnected(false);
    };
    
    // 建立连接
    wsManagerRef.current.connect();
    
    // 清理函数
    return () => {
      if (wsManagerRef.current) {
        wsManagerRef.current.disconnect();
      }
    };
  }, []);

  const sendMessage = (message) => {
    if (wsManagerRef.current?.ws?.readyState === WebSocket.OPEN) {
      wsManagerRef.current.ws.send(JSON.stringify(message));
    }
  };

  return {
    isConnected,
    notifications,
    sendMessage
  };
};

// 使用示例
const App = () => {
  const { isConnected, notifications } = useWebSocket();

  return (
    <div>
      <div>WebSocket状态: {isConnected ? '已连接' : '未连接'}</div>
      <div>通知数量: {notifications.length}</div>
    </div>
  );
};
```

## 连接时机建议

### 1. 用户登录后立即连接
```javascript
// 在用户登录成功后建立连接
const handleLogin = async (credentials) => {
  try {
    const loginResult = await login(credentials);
    if (loginResult.success) {
      // 登录成功后立即建立WebSocket连接
      webSocketManager.connect();
    }
  } catch (error) {
    console.error('登录失败', error);
  }
};
```

### 2. 应用启动时检查登录状态
```javascript
// 应用启动时检查用户是否已登录
const initializeApp = async () => {
  try {
    const userInfo = await getCurrentUser();
    if (userInfo) {
      // 用户已登录，建立WebSocket连接
      webSocketManager.connect();
    }
  } catch (error) {
    console.log('用户未登录');
  }
};
```

### 3. 页面可见性变化时重连
```javascript
// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'visible') {
    // 页面变为可见时，检查连接状态
    if (!webSocketManager.isConnected()) {
      webSocketManager.connect();
    }
  }
});
```

### 4. 报告生成页面特殊处理
```javascript
// 在报告生成相关页面，确保连接已建立
const ReportGenerationPage = () => {
  useEffect(() => {
    // 确保WebSocket连接已建立
    if (!webSocketManager.isConnected()) {
      webSocketManager.connect();
    }
  }, []);

  const handleGenerateReport = async (reportData) => {
    try {
      // 开始生成报告
      await generateReport(reportData);
      
      // 显示生成中提示
      showMessage('报告正在生成中，完成后将通过通知提醒您');
      
    } catch (error) {
      console.error('生成报告失败', error);
    }
  };

  return (
    <div>
      {/* 报告生成界面 */}
    </div>
  );
};
```

## 最佳实践

### 1. 连接管理
- 用户登录后立即建立连接
- 页面刷新或重新打开时自动重连
- 网络断开后自动重连（带重试限制）
- 用户退出登录时主动断开连接

### 2. 错误处理
- 连接失败时显示友好提示
- 重连次数限制，避免无限重连
- 网络状态监听，网络恢复时重连

### 3. 性能优化
- 避免频繁建立/断开连接
- 合理设置重连间隔
- 及时清理事件监听器

### 4. 用户体验
- 连接状态可视化显示
- 通知消息去重处理
- 重要通知持久化存储

这样的实现确保了WebSocket连接能够适应不同的部署环境，同时提供了良好的用户体验和可靠的连接管理。

# 数据库查询超时优化指南

## 问题描述
系统出现大量MySQL查询超时异常：`MySQLTimeoutException: Statement cancelled due to timeout or client request`

## 根本原因分析

### 1. 查询超时设置过短
- 原配置：30-60秒
- 复杂聚合查询需要更长时间

### 2. 低效的SQL查询
- 使用 `IN (SELECT DISTINCT cid FROM company_node)` 子查询
- 缺少合适的索引
- 大表全表扫描

### 3. 并发压力过大
- 线程池配置过大导致数据库连接池压力
- 异步查询并发执行

## 已实施的优化措施

### 1. 调整查询超时时间
```yaml
# 从30秒增加到120秒
default-statement-timeout: 120
```

### 2. 优化SQL查询
将子查询改为JOIN查询：
```sql
-- 优化前
SELECT YEAR, ROUND((sum(net_profit) / sum(total_assets - total_liabilty)) * 100, 2) AS data
FROM company_finance
WHERE YEAR >= '2020' AND quarter = 4
  AND cid IN (SELECT DISTINCT cid FROM company_node)

-- 优化后  
SELECT cf.YEAR, ROUND((sum(cf.net_profit) / sum(cf.total_assets - cf.total_liabilty)) * 100, 2) AS data
FROM company_finance cf
INNER JOIN company_node cn ON cf.cid = cn.cid AND cn.is_valid = 1
WHERE cf.YEAR >= '2020' AND cf.quarter = 4
```

### 3. 优化线程池配置
- 核心线程数：20 → 15
- 最大线程数：60 → 40  
- 队列容量：2000 → 3000
- 拒绝策略：DiscardOldestPolicy → CallerRunsPolicy

## 建议的进一步优化

### 1. 数据库索引优化
```sql
-- 为company_finance表添加复合索引
CREATE INDEX idx_company_finance_year_quarter_cid ON company_finance(YEAR, quarter, cid);
CREATE INDEX idx_company_finance_cid_year ON company_finance(cid, YEAR);

-- 为company_node表添加索引
CREATE INDEX idx_company_node_cid_valid ON company_node(cid, is_valid);
CREATE INDEX idx_company_node_region ON company_node(province, city, area, is_valid);
```

### 2. 查询缓存策略
- 对于不经常变化的数据使用Redis缓存
- 设置合理的缓存过期时间
- 使用缓存预热策略

### 3. 分页查询优化
- 避免深度分页
- 使用游标分页替代OFFSET
- 限制单次查询数据量

### 4. 监控和告警
- 添加慢查询监控
- 设置数据库连接池使用率告警
- 监控查询超时频率

## 监控指标

### 关键指标
1. 查询响应时间
2. 数据库连接池使用率
3. 慢查询数量
4. 超时异常频率

### 告警阈值
- 连接池使用率 > 80%
- 查询响应时间 > 60秒
- 超时异常 > 10次/分钟

## 应急处理方案

### 1. 临时措施
- 重启应用释放连接池
- 增加数据库连接池大小
- 临时禁用非关键查询

### 2. 长期方案
- 数据库读写分离
- 查询结果缓存
- 数据库分库分表

## 注意事项
1. 修改超时时间需要重启应用
2. SQL优化需要充分测试
3. 线程池调整需要观察系统负载
4. 索引添加需要在业务低峰期进行

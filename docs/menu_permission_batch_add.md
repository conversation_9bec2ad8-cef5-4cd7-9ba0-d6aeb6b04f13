# 菜单权限批量添加功能

## 功能概述

本功能用于为拥有指定菜单权限的用户批量添加新的菜单权限。特别是针对新增菜单后，需要为已有相关权限的用户自动分配新菜单权限的场景。

## 业务场景

当系统新增了菜单（如菜单ID为222），需要为所有拥有某个相关菜单权限（如菜单ID为4）的用户自动添加新菜单的访问权限。

## 实现方案

### 1. 核心Service方法

在 `ISysUserMenuService` 接口中新增方法：

```java
/**
 * 为拥有指定菜单权限的用户批量添加新菜单权限
 * 
 * @param sourceMenuId 源菜单ID（用户必须拥有此菜单权限）
 * @param targetMenuId 目标菜单ID（要添加的新菜单权限）
 * @return 成功添加权限的用户数量
 */
int batchAddMenuPermissionForUsersWithSourceMenu(Long sourceMenuId, Long targetMenuId);
```

### 2. 实现逻辑

1. **查询正常状态用户**：查询 `status=1` 且 `valid_flag=1` 的用户
2. **筛选拥有源菜单权限的用户**：从 `sys_user_menu` 表中查询拥有源菜单权限的用户
3. **排除已有目标菜单权限的用户**：避免重复添加权限
4. **批量插入新权限记录**：为符合条件的用户添加目标菜单权限
5. **事务保证**：整个操作在事务中执行，确保数据一致性

### 3. API接口

#### 通用接口
```
POST /back/system/menu-permission/batch-add-by-source-menu
参数：
- sourceMenuId: 源菜单ID
- targetMenuId: 目标菜单ID
```

#### 专用接口（针对菜单4和222）
```
POST /back/system/menu-permission/add-menu-222-for-menu-4-users
无需参数，直接为拥有菜单ID为4权限的用户添加菜单ID为222的权限
```

#### 智能问答和智能体权限接口
```
POST /back/system/menu-permission/add-intelligent-qa-and-agent-permissions-for-all-users
无需参数，为所有正常状态的用户添加智能问答菜单（ID=8）权限以及所有启用的智能体权限
```

## 使用方法

### 1. 通过API调用

#### 使用通用接口
```bash
curl -X POST "http://localhost:8080/back/system/menu-permission/batch-add-by-source-menu?sourceMenuId=4&targetMenuId=222" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 使用专用接口
```bash
curl -X POST "http://localhost:8080/back/system/menu-permission/add-menu-222-for-menu-4-users" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 使用智能问答和智能体权限接口
```bash
curl -X POST "http://localhost:8080/back/system/menu-permission/add-intelligent-qa-and-agent-permissions-for-all-users" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 通过单元测试

运行测试类 `SysUserMenuServiceTest` 中的测试方法：

```bash
mvn test -Dtest=SysUserMenuServiceTest#testBatchAddMenuPermissionForUsersWithSourceMenu
```

## 数据验证

### 执行前检查

运行 `scripts/check_menu_permissions.sql` 脚本来检查：

1. 菜单ID为4和222的菜单是否存在
2. 正常状态用户数量
3. 拥有各菜单权限的用户数量
4. 需要添加权限的用户详情

### 执行后验证

再次运行检查脚本，确认：

1. 拥有菜单ID为222权限的用户数量增加
2. 同时拥有菜单ID为4和222权限的用户数量增加
3. 没有重复的权限记录

## 安全考虑

1. **权限控制**：接口需要管理员权限才能访问
2. **参数校验**：严格校验输入参数，防止SQL注入
3. **事务回滚**：操作失败时自动回滚，保证数据一致性
4. **日志记录**：详细记录操作过程和结果

## 注意事项

1. **幂等性**：重复执行不会产生重复的权限记录
2. **性能考虑**：使用批量操作提高性能，避免循环中的数据库查询
3. **数据完整性**：确保菜单ID存在且有效
4. **用户状态**：只为正常状态的用户添加权限

## 错误处理

- 源菜单ID或目标菜单ID为空：返回参数错误
- 源菜单ID和目标菜单ID相同：返回参数错误
- 数据库操作失败：事务回滚并返回错误信息
- 未找到符合条件的用户：返回成功但添加数量为0

## 智能体权限管理

### 智能体菜单ID生成规则

智能体菜单采用动态生成的方式，菜单ID计算规则为：
```java
Long agentMenuId = Math.abs(agentKey.hashCode()) + UserConstants.AGENT_MENU_ID_OFFSET;
```

其中：
- `agentKey`：智能体的唯一标识符
- `AGENT_MENU_ID_OFFSET = 1000000000L`：偏移量，确保与系统固定菜单ID不冲突

### 智能体权限特点

1. **动态性**：智能体菜单不存储在`sys_menu`表中，而是根据`sys_agent_config`表动态生成
2. **层级关系**：所有智能体菜单的父菜单都是智能问答菜单（ID=8）
3. **权限控制**：每个智能体都有独立的菜单权限，存储在`sys_user_menu`表中
4. **启用状态**：只有`enabled=true`的智能体才会生成对应的菜单权限

### 批量添加逻辑

`batchAddIntelligentQAAndAgentPermissionsForAllUsers`方法会：

1. 查询所有正常状态的用户
2. 查询所有启用的智能体配置
3. 为每个用户添加智能问答菜单（ID=8）权限
4. 为每个用户添加所有启用智能体的菜单权限
5. 避免重复添加已存在的权限

## 扩展性

该功能设计为通用的菜单权限批量添加工具，可以用于任何类似的权限分配场景，不仅限于菜单ID为4和222的情况。智能体权限管理功能展示了如何处理动态菜单权限的批量分配。

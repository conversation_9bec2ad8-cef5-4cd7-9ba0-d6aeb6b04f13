# 本地化部署账号级别调整说明

## 背景

系统原本设计为saas化版本，支持多个主账号，每个主账号下可以有多个子账号。现在需要支持本地化部署，在本地化部署中只有一个用户ID为1的主账号，其他都是子账号。

## 账号级别定义

- `accountLevel = 0`: 原有账号（暂不处理）
- `accountLevel = 1`: 主账号
- `accountLevel = 2`: 子账号

## 部署模式配置

### 配置项

在配置文件中添加了部署模式配置：

```yaml
# 部署模式配置
deployment:
  mode: saas  # 可选值: saas(saas版本), local(本地化部署)
```

### 配置文件

- `application.yml`: 默认为saas模式
- `application-localproductionk8s.yml`: 本地化正式环境，设置为local模式
- `application-localtestk8s.yml`: 本地化测试环境，设置为local模式

## 主要修改内容

### 1. 登录逻辑调整

#### SysLoginService.login()方法

**saas版本**：
- 子账号登录格式：`主账号:子账号`
- 系统通过`:`或`：`分隔符识别子账号登录

**本地化部署**：
- 子账号登录格式：直接使用用户名，无需前缀
- 系统自动判断非管理员账号（用户ID不为1）为子账号
- 主账号固定为用户ID为1的管理员账号

#### 代码示例

```java
// 判断是否为子账号登录
boolean isSubAccountLogin = false;
String mainAccountName = null;
String subAccountName = null;

// saas版本：子账号登录格式是主账号:子账号
if (deploymentConfig.isSaasDeployment() && (phone.contains(":") || phone.contains("："))) {
    isSubAccountLogin = true;
    // 解析主账号和子账号
}
// 本地化部署：直接按子账号处理（除非是管理员账号）
else if (deploymentConfig.isLocalDeployment()) {
    final SysUser tempUser = loadUserByAccountWithNoException(phone);
    if (tempUser != null && !UserConstants.ADMIN_ID.equals(tempUser.getUserId())) {
        isSubAccountLogin = true;
        mainAccountName = "admin"; // 本地化部署默认主账号为admin
        subAccountName = phone;
    }
}
```

### 2. 用户查询逻辑调整

#### SysUserService.getMainAccountId()方法

**saas版本**：
- 根据当前用户的账号级别和账号关联表确定主账号ID

**本地化部署**：
- 主账号固定返回用户ID为1

#### SysUserService.getMainAndSubAccountId()方法

**saas版本**：
- 通过账号关联表查询主账号下的所有子账号

**本地化部署**：
- 返回所有有效用户ID（主账号固定为1）

### 3. 用户创建逻辑调整

#### SysUserService.insertUser()方法

**saas版本**：
- 创建用户后需要在账号关联表中建立主子账号关系

**本地化部署**：
- 创建用户后同样建立账号关联关系，但所有子账号都关联到管理员账号（用户ID为1）
- 这样保持了数据结构的一致性，便于统一处理

### 4. 用户列表查询调整

#### SysUserService.selectPageUserList()方法

**saas版本**：
- 通过账号关联表查询当前主账号下的子账号

**本地化部署**：
- 同样通过账号关联表查询，但由于所有子账号都关联到管理员账号，实际上会查询到所有子账号

## 使用说明

### 本地化部署登录

1. **管理员登录**：
   - 用户名：admin（或管理员的实际用户名）
   - 密码：管理员密码
   - 登录后具有管理员权限

2. **子账号登录**：
   - 用户名：直接使用子账号用户名，无需前缀
   - 密码：子账号密码
   - 系统自动识别为子账号并关联到管理员主账号

### saas版本登录

1. **主账号登录**：
   - 用户名：主账号用户名
   - 密码：主账号密码

2. **子账号登录**：
   - 用户名：`主账号:子账号`格式
   - 密码：子账号密码

## 注意事项

1. 本地化部署时，用户ID为1的账号必须存在且为管理员账号
2. 本地化部署时，所有非管理员账号都被视为子账号
3. 配置文件中的`deployment.mode`必须正确设置
4. 本地化部署时，账号关联表仍然会被使用，所有子账号都关联到管理员账号
5. 这样设计保持了数据结构的一致性，便于代码逻辑的统一处理

## 测试验证

可以通过以下方式验证修改是否正确：

1. 设置`deployment.mode=local`
2. 使用子账号用户名直接登录（无需主账号前缀）
3. 验证登录成功且权限正确
4. 验证用户管理功能正常

## 其他调整内容

### 1. 体验账号限制调整
- **saas版本**：体验账号只能访问固定的产业链
- **本地化部署**：所有用户都可以访问所有产业链，无限制

### 2. API使用限制调整
- **saas版本**：体验账号有API调用次数限制
- **本地化部署**：所有用户都无API调用次数限制

### 3. 数据权限控制调整
- **saas版本**：根据用户权限配置控制数据访问
- **本地化部署**：所有用户都有全部数据权限

### 4. 权限验证逻辑调整
- **saas版本**：严格按照角色和权限配置验证
- **本地化部署**：所有用户都有管理员权限

### 5. 账号到期检查调整
- **saas版本**：定时检查账号到期状态，自动更新过期账号
- **本地化部署**：跳过账号到期检查，账号永不过期

### 6. 数据导出限制调整
- **saas版本**：每日导出次数有限制
- **本地化部署**：无导出次数限制

## 兼容性

此修改完全向后兼容，不会影响现有的saas版本功能。通过配置文件可以灵活切换部署模式。

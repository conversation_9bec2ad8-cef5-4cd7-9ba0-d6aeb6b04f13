-- ========================================
-- RegionFilterHelper 索引优化方案
-- 基于查询模式分析的必要索引建议
-- ========================================

-- ==========================================
-- 🚀 高优先级索引（立即建立）- 影响最大
-- ==========================================

-- 1. company_node 地区筛选核心索引（最重要）
-- 支持: getCompanyCountWithRegion, getRegionListWithRegion 等核心查询
-- 查询模式: WHERE is_valid = 1 AND province = ? AND city = ? AND area = ?
CREATE INDEX idx_company_node_region_valid ON company_node (is_valid, province, city, area);

-- 2. company_node 节点ID + 地区筛选复合索引
-- 支持: getCompanyNodeCountWithRegion, aggTagWithRegion 等查询
-- 查询模式: WHERE node_id IN (...) AND is_valid = 1 AND province = ?
CREATE INDEX idx_company_node_node_region ON company_node (node_id, is_valid, province, city, area);

-- 3. company_tag 企业ID索引（关联查询优化）
-- 支持: aggTagWithRegion 中的 JOIN company_tag ct ON icn.cid = ct.cid
-- 查询模式: JOIN 关联查询优化
CREATE INDEX idx_company_tag_cid ON company_tag (cid);

-- 4. company_tag 标签名称索引
-- 支持: aggTagWithRegion 中的 WHERE ct.tag_name IS NOT NULL
-- 查询模式: WHERE tag_name IN ('A股', 'B股', '港股', '中概股')
CREATE INDEX idx_company_tag_name ON company_tag (tag_name);

-- ==========================================
-- 📈 中优先级索引（重要但非紧急）
-- ==========================================

-- 5. company_node 企业ID + 有效性索引（用于关联查询）
-- 支持: 各种 JOIN company_node 的查询
CREATE INDEX idx_company_node_cid_valid ON company_node (cid, is_valid);

-- 6. patent_node 地区筛选索引
-- 支持: getOveriewPatentApplyWithRegion, getPatentDomainWithRegion
-- 查询模式: WHERE is_valid = 1 AND province = ? AND city = ?
CREATE INDEX idx_patent_node_region_valid ON patent_node (is_valid, province, city);

-- 7. patent_node 专利类型 + 日期索引
-- 支持: getOveriewPatentApplyWithRegion 中的时间和类型筛选
-- 查询模式: WHERE patent_type = '发明授权' AND public_date > '2017-01-01'
CREATE INDEX idx_patent_node_type_date ON patent_node (patent_type, public_date, is_valid);

-- ==========================================
-- 📊 低优先级索引（可选优化）
-- ==========================================

-- 8. company_node 建立日期相关索引（用于时间筛选）
CREATE INDEX idx_company_node_establish_date ON company_node (establish_date, is_valid);

-- 9. patent_node 主IPC + 有效性索引（用于技术分类查询）
CREATE INDEX idx_patent_node_ipc_valid ON patent_node (main_ipc, is_valid);

-- 10. dm_division 主键优化（如果使用字符串主键）
-- 支持: RegionQueryUtil.getRegionQueryCondition 中的 selectById
CREATE INDEX idx_dm_division_id ON dm_division (id);

-- ==========================================
-- 🎯 覆盖索引（性能极致优化）
-- ==========================================

-- 11. company_node COUNT查询覆盖索引
-- 专门优化 SELECT COUNT(DISTINCT cid) 类型的查询
CREATE INDEX idx_company_node_count_cover ON company_node (is_valid, cid, province, city, area);

-- 12. company_tag + company_node 关联查询覆盖索引
-- 优化 aggTagWithRegion 类型的复杂关联查询
CREATE INDEX idx_company_tag_cid_name ON company_tag (cid, tag_name);
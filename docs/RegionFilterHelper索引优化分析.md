# RegionFilterHelper 索引优化分析报告

## 📋 概述

本报告基于对 `RegionFilterHelper` 相关查询的深入分析，提供了针对性的数据库索引优化建议。通过建立合适的索引，可以显著提升地区筛选查询的性能。

## 🔍 查询模式分析

### 核心查询场景

1. **企业数量统计查询**
   - `getCompanyCountWithRegion`: 统计指定地区的企业数量
   - 查询模式: `SELECT COUNT(DISTINCT cid) FROM company_node WHERE is_valid = 1 AND province = ? AND city = ? AND area = ?`

2. **地区分布查询**
   - `getRegionListWithRegion`: 获取企业的地区分布统计
   - 查询模式: `SELECT city/province/area, COUNT(*) FROM company_node WHERE is_valid = 1 AND province = ? GROUP BY city/province/area`

3. **标签聚合查询**
   - `aggTagWithRegion`: 统计指定地区和节点的企业标签分布
   - 查询模式: `SELECT tag_name, COUNT(DISTINCT cid) FROM company_node JOIN company_tag WHERE node_id IN (...) AND province = ?`

4. **专利相关查询**
   - `getOveriewPatentApplyWithRegion`: 专利申请统计
   - 查询模式: `SELECT COUNT(DISTINCT patent_id) FROM patent_node WHERE province = ? AND patent_type = ?`

## 🎯 索引优化策略

### 高优先级索引（立即建立）

#### 1. company_node 地区筛选核心索引 ⭐⭐⭐⭐⭐
```sql
CREATE INDEX idx_company_node_region_valid ON company_node (is_valid, province, city, area);
```
**影响查询**: 所有 `*WithRegion` 方法的基础筛选
**优化效果**: 预计查询性能提升 80-90%
**必要性**: 极高，这是最核心的索引

#### 2. company_node 节点+地区复合索引 ⭐⭐⭐⭐⭐
```sql
CREATE INDEX idx_company_node_node_region ON company_node (node_id, is_valid, province, city, area);
```
**影响查询**: `getCompanyNodeCountWithRegion`, `aggTagWithRegion`
**优化效果**: 预计查询性能提升 70-85%
**必要性**: 极高，支持节点筛选+地区筛选的组合查询

#### 3. company_tag 企业ID索引 ⭐⭐⭐⭐
```sql
CREATE INDEX idx_company_tag_cid ON company_tag (cid);
```
**影响查询**: `aggTagWithRegion` 中的 JOIN 操作
**优化效果**: 预计关联查询性能提升 60-75%
**必要性**: 高，优化关联查询性能

#### 4. company_tag 标签名称索引 ⭐⭐⭐⭐
```sql
CREATE INDEX idx_company_tag_name ON company_tag (tag_name);
```
**影响查询**: 标签筛选和聚合查询
**优化效果**: 预计标签查询性能提升 50-70%
**必要性**: 高，优化标签相关查询

### 中优先级索引（重要但非紧急）

#### 5. patent_node 地区筛选索引 ⭐⭐⭐
```sql
CREATE INDEX idx_patent_node_region_valid ON patent_node (is_valid, province, city);
```
**影响查询**: `getOveriewPatentApplyWithRegion`, `getPatentDomainWithRegion`
**优化效果**: 预计专利查询性能提升 60-80%

#### 6. patent_node 专利类型+日期索引 ⭐⭐⭐
```sql
CREATE INDEX idx_patent_node_type_date ON patent_node (patent_type, public_date, is_valid);
```
**影响查询**: 专利申请趋势分析
**优化效果**: 预计时间序列查询性能提升 50-70%

### 低优先级索引（可选优化）

#### 7. company_node 企业ID索引 ⭐⭐
```sql
CREATE INDEX idx_company_node_cid_valid ON company_node (cid, is_valid);
```
**影响查询**: 各种关联查询
**优化效果**: 预计关联查询性能提升 30-50%

## 📊 索引建立建议

### 立即建立（第一批）
```sql
-- 最核心的4个索引，建议立即建立
CREATE INDEX idx_company_node_region_valid ON company_node (is_valid, province, city, area);
CREATE INDEX idx_company_node_node_region ON company_node (node_id, is_valid, province, city, area);
CREATE INDEX idx_company_tag_cid ON company_tag (cid);
CREATE INDEX idx_company_tag_name ON company_tag (tag_name);
```

### 后续建立（第二批）
```sql
-- 专利相关索引
CREATE INDEX idx_patent_node_region_valid ON patent_node (is_valid, province, city);
CREATE INDEX idx_patent_node_type_date ON patent_node (patent_type, public_date, is_valid);
```

### 可选建立（第三批）
```sql
-- 其他优化索引
CREATE INDEX idx_company_node_cid_valid ON company_node (cid, is_valid);
CREATE INDEX idx_company_node_establish_date ON company_node (establish_date, is_valid);
```

## ⚠️ 注意事项

### 索引维护成本
- 每个索引都会增加写操作的成本
- 建议优先建立高优先级索引，观察效果后再考虑其他索引

### 存储空间考虑
- 复合索引会占用较多存储空间
- 建议定期监控索引使用情况，删除未使用的索引

### 动态表名处理
- 由于使用了动态表名（如 `chainId_company_node`），需要为每个产业链表分别建立索引
- 建议编写脚本批量创建索引

## 📈 预期效果

### 性能提升预期
- **地区筛选查询**: 性能提升 80-90%
- **节点+地区组合查询**: 性能提升 70-85%
- **标签聚合查询**: 性能提升 60-75%
- **专利地区查询**: 性能提升 60-80%

### 系统稳定性提升
- 减少查询超时
- 降低数据库CPU使用率
- 提高并发处理能力

## 🚀 实施计划

1. **第一阶段**（立即执行）: 建立4个高优先级索引
2. **第二阶段**（1周后）: 建立专利相关索引
3. **第三阶段**（2周后）: 根据监控数据决定是否建立其他索引
4. **持续监控**: 定期分析索引使用情况和查询性能

# 动态表索引批量创建使用说明

## 📋 概述

本工具用于为所有产业链的动态表（`company_node` 和 `patent_node`）批量创建索引，解决RegionFilterHelper相关查询的性能问题。

## 🎯 支持的表和索引

### company_node 表索引
1. **地区筛选核心索引**：`(is_valid, province, city, area)`
2. **节点+地区复合索引**：`(node_id, is_valid, province, city, area)`
3. **企业ID索引**：`(cid, is_valid)`
4. **建立日期索引**：`(establish_date, is_valid)`

### patent_node 表索引
1. **地区筛选索引**：`(is_valid, province, city)`
2. **专利类型+日期索引**：`(patent_type, public_date, is_valid)`
3. **节点ID索引**：`(node_id, is_valid)`
4. **主IPC索引**：`(main_ipc, is_valid)`

## 🚀 使用方法

### 方法一：通过API接口（推荐）

#### 1. 查看所有产业链
```bash
curl -X GET "http://localhost:8612/api/index-management/chain-list"
```

#### 2. 查看表统计信息
```bash
curl -X GET "http://localhost:8612/api/index-management/table-statistics"
```

#### 3. 创建所有索引（一键执行）
```bash
curl -X POST "http://localhost:8612/api/index-management/create-all-indexes"
```

#### 4. 仅创建 company_node 表索引
```bash
curl -X POST "http://localhost:8612/api/index-management/create-company-node-indexes"
```

#### 5. 仅创建 patent_node 表索引
```bash
curl -X POST "http://localhost:8612/api/index-management/create-patent-node-indexes"
```

#### 6. 为指定产业链创建索引
```bash
curl -X POST "http://localhost:8612/api/index-management/create-indexes-for-chain/industry_ai"
```

#### 7. 删除指定产业链的索引（重建前）
```bash
curl -X DELETE "http://localhost:8612/api/index-management/drop-indexes/industry_ai"
```

### 方法二：通过SQL脚本

#### 1. 执行查询脚本
```bash
# 进入项目根目录
cd /path/to/icir

# 连接数据库执行脚本
mysql -h your_host -u your_user -p your_database < scripts/create_dynamic_table_indexes.sql
```

#### 2. 手动执行生成的SQL
1. 打开 `scripts/create_dynamic_table_indexes.sql`
2. 执行第一步的查询语句，获取所有产业链ID
3. 执行第二步的生成语句，获取索引创建SQL
4. 复制生成的SQL语句到数据库客户端执行

### 方法三：通过Java代码

```java
@Autowired
private DynamicTableIndexManager indexManager;

// 创建所有 company_node 表索引
indexManager.createCompanyNodeIndexes();

// 创建所有 patent_node 表索引
indexManager.createPatentNodeIndexes();

// 查看表统计信息
indexManager.printTableStatistics();

// 删除指定产业链的索引
indexManager.dropIndexesForChain("industry_ai");
```

## 📊 执行示例

### 1. 查看产业链列表
```json
{
  "status": "SUCCESS",
  "totalCount": 25,
  "chains": [
    {
      "nameEn": "industry_ai",
      "name": "人工智能",
      "label": "新兴产业"
    },
    {
      "nameEn": "ningbo_new_energy_vehicle",
      "name": "宁波新能源汽车",
      "label": "区域产业"
    }
  ]
}
```

### 2. 执行索引创建
```json
{
  "status": "SUCCESS",
  "message": "所有动态表索引创建完成，请查看日志了解详细结果"
}
```

### 3. 日志输出示例
```
2025-07-18 10:00:00 INFO  - 开始为 25 个产业链创建 company_node 表索引
2025-07-18 10:00:01 INFO  - ✅ 产业链 人工智能 (industry_ai) 的 company_node 索引创建成功
2025-07-18 10:00:02 INFO  - ✅ 产业链 宁波新能源汽车 (ningbo_new_energy_vehicle) 的 company_node 索引创建成功
2025-07-18 10:00:03 WARN  - ⚠️ 表 test_chain_company_node 不存在，跳过索引创建
2025-07-18 10:00:05 INFO  - company_node 索引创建完成: 成功 23, 失败 0
```

## ⚠️ 注意事项

### 1. 执行前检查
- 确保数据库连接正常
- 确认有足够的磁盘空间（索引会占用额外空间）
- 建议在业务低峰期执行

### 2. 安全措施
- 工具会自动检查表是否存在
- 使用 `CREATE INDEX IF NOT EXISTS` 避免重复创建
- 支持单独删除和重建索引

### 3. 性能影响
- 索引创建过程中可能影响查询性能
- 大表的索引创建可能需要较长时间
- 建议分批执行，先创建最重要的索引

### 4. 监控建议
- 执行过程中监控数据库CPU和IO使用率
- 关注慢查询日志
- 检查索引创建是否成功

## 🔍 故障排查

### 1. 索引创建失败
```bash
# 检查错误日志
tail -f logs/app.log | grep -E "(索引创建失败|ERROR)"

# 检查数据库错误日志
tail -f /var/log/mysql/error.log
```

### 2. 表不存在
```sql
-- 检查特定产业链的表是否存在
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name LIKE '%company_node%';
```

### 3. 索引状态检查
```sql
-- 检查索引是否创建成功
SELECT table_name, index_name, column_name
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND index_name LIKE 'idx_%_company_node_%'
ORDER BY table_name, index_name;
```

## 📈 预期效果

### 性能提升
- 地区筛选查询：**80-90%** 性能提升
- 节点+地区组合查询：**70-85%** 性能提升
- 标签聚合查询：**60-75%** 性能提升

### 系统稳定性
- 减少慢查询数量
- 降低数据库连接池压力
- 提高并发处理能力

## 🔄 维护建议

### 1. 定期检查
- 每月检查索引使用情况
- 监控慢查询日志
- 评估索引效果

### 2. 新产业链处理
- 新增产业链时自动创建索引
- 定期扫描是否有遗漏的表

### 3. 索引优化
- 根据查询模式调整索引
- 删除未使用的索引
- 考虑添加覆盖索引

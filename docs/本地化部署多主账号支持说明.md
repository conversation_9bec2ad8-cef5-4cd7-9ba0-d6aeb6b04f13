# 本地化部署多主账号支持说明

## 概述

本次修改解决了本地化部署时只支持单一主账号（admin）的问题，现在支持多个主账号同时存在和登录。

## 问题背景

在本地化部署环境中，原有逻辑存在以下问题：
1. 只将用户ID为1的账号视为主账号
2. 其他所有账号都被当作子账号处理，即使它们在数据库中的 `account_level` 字段标记为主账号
3. 这导致初始化时创建的多个主账号无法正常登录

## 修改内容

### 1. 登录逻辑调整 (SysLoginService.login)

**修改前**：
```java
// 本地化部署：直接按子账号处理（除非是管理员账号）
else if (deploymentConfig.isLocalDeployment()) {
    // 先查询用户，判断是否为管理员账号（用户ID为1）
    final SysUser tempUser = loadUserByAccountWithNoException(phone);
    if (tempUser != null && !UserConstants.ADMIN_ID.equals(tempUser.getUserId())) {
        isSubAccountLogin = true;
        mainAccountName = "admin"; // 本地化部署默认主账号为admin
        subAccountName = phone;
    }
}
```

**修改后**：
```java
// 本地化部署：根据账号级别判断是否为子账号
else if (deploymentConfig.isLocalDeployment()) {
    // 先查询用户，判断账号级别
    final SysUser tempUser = loadUserByAccountWithNoException(phone);
    if (tempUser != null && AccountLevelEnum.SUB.getId().equals(tempUser.getAccountLevel())) {
        isSubAccountLogin = true;
        // 查找该子账号对应的主账号
        final SysAccountRelation accountRelation = sysAccountRelationMapper.selectOne(
            Wrappers.<SysAccountRelation>lambdaQuery()
                .eq(SysAccountRelation::getSubAccountId, tempUser.getUserId())
        );
        if (accountRelation != null) {
            final SysUser mainUser = sysUserMapper.selectById(accountRelation.getMainAccountId());
            if (mainUser != null) {
                mainAccountName = mainUser.getUserName();
            } else {
                mainAccountName = "admin";
            }
        } else {
            mainAccountName = "admin";
        }
        subAccountName = phone;
    }
}
```

**关键变化**：
- 不再基于用户ID判断，而是基于 `account_level` 字段判断
- 支持查找子账号对应的真实主账号
- 保持向后兼容性

### 2. 主账号查询逻辑调整 (SysLoginService.loadUserByMainAccount)

**修改前**：
```java
// 本地化部署时，如果是admin，直接返回用户ID为1的管理员账号
if (deploymentConfig.isLocalDeployment() && "admin".equals(phoneNumber)) {
    final SysUser adminUser = sysUserMapper.selectById(UserConstants.ADMIN_ID);
    if (adminUser == null) {
        log.info("本地化部署管理员账号不存在");
        throw new BusinessException("用户名或密码错误！");
    }
    return adminUser;
}
```

**修改后**：
```java
// 移除了特殊的admin处理逻辑，统一按照用户名/手机号查询
// 添加了本地化部署时子账号直接登录的检查
else if (deploymentConfig.isLocalDeployment() && AccountLevelEnum.SUB.getId().equals(user.getAccountLevel()) && needSubCheck) {
    log.info("本地化部署子账号直接登录：{} ", phoneNumber);
    throw new BusinessException("用户名或密码错误！");
}
```

**关键变化**：
- 移除了硬编码的admin特殊处理
- 允许任何主账号用户名登录
- 增加了子账号直接登录的检查

### 3. 主账号ID获取逻辑调整 (SysUserService.getMainAccountId)

**修改前**：
```java
// 本地化部署时，主账号固定为用户ID为1的管理员账号
if (deploymentConfig.isLocalDeployment()) {
    return UserConstants.ADMIN_ID;
}
```

**修改后**：
```java
// 本地化部署时，根据账号级别判断
if (deploymentConfig.isLocalDeployment()) {
    // 如果当前用户是主账号，返回自己的ID
    if (Objects.equals(currentUser.getAccountLevel(), AccountLevelEnum.MAIN.getId()) 
        || Objects.equals(currentUser.getAccountLevel(), AccountLevelEnum.ORIGINAL.getId())) {
        return currentUserId;
    }
    // 如果是子账号，查找对应的主账号ID
    else if (Objects.equals(currentUser.getAccountLevel(), AccountLevelEnum.SUB.getId())) {
        final SysAccountRelation sysAccountRelation = sysAccountRelationMapper.selectOne(Wrappers.<SysAccountRelation>lambdaQuery()
                .eq(SysAccountRelation::getSubAccountId, currentUserId));
        if (Objects.nonNull(sysAccountRelation)) {
            return sysAccountRelation.getMainAccountId();
        } else {
            return UserConstants.ADMIN_ID;
        }
    }
    return UserConstants.ADMIN_ID;
}
```

**关键变化**：
- 支持多个主账号，不再固定返回用户ID为1
- 正确处理原账号(ORIGINAL)和主账号(MAIN)
- 子账号能正确找到对应的主账号

## 账号级别说明

根据 `AccountLevelEnum` 枚举：
- `ORIGINAL(0, "原账号")`：初始化时创建的账号，在本地化部署中视为主账号
- `MAIN(1, "主账号")`：标准的主账号
- `SUB(2, "子账号")`：子账号，需要关联到某个主账号

## 使用场景

### 场景1：多个初始化主账号
```
用户A (ID=1, account_level=0, username="admin")     -> 可以直接登录，作为主账号
用户B (ID=2, account_level=0, username="manager")   -> 可以直接登录，作为主账号  
用户C (ID=3, account_level=1, username="leader")    -> 可以直接登录，作为主账号
```

### 场景2：主账号下的子账号
```
用户A (ID=1, account_level=0, username="admin")     -> 主账号
用户D (ID=4, account_level=2, username="user1")     -> 子账号，关联到用户A
用户E (ID=5, account_level=2, username="user2")     -> 子账号，关联到用户B
```

## 兼容性

- 保持与现有SAAS版本的完全兼容
- 保持与现有本地化部署的向后兼容
- 不影响现有的权限和菜单逻辑

## 测试建议

1. 测试多个主账号登录
2. 测试子账号登录（应该失败）
3. 测试主账号创建子账号的功能
4. 测试权限和菜单显示是否正常

# 动态表索引创建工具 - 快速使用指南

## 🎯 目标
解决RegionFilterHelper相关查询的性能问题，为所有产业链的动态表批量创建索引。

## 🚀 快速开始

### 1. 一键创建所有索引（推荐）
```bash
# 方法一：使用Shell脚本
./scripts/create_indexes.sh --all

# 方法二：使用API
curl -X POST "http://localhost:8612/api/index-management/create-all-indexes"
```

### 2. 查看产业链列表
```bash
# 查看所有产业链
./scripts/create_indexes.sh --list

# 查看表统计信息
./scripts/create_indexes.sh --statistics
```

### 3. 分步创建索引
```bash
# 仅创建 company_node 表索引
./scripts/create_indexes.sh --company

# 仅创建 patent_node 表索引
./scripts/create_indexes.sh --patent
```

## 📊 创建的索引

### company_node 表（每个产业链）
- `idx_{chainId}_company_node_region_valid` - 地区筛选核心索引
- `idx_{chainId}_company_node_node_region` - 节点+地区复合索引  
- `idx_{chainId}_company_node_cid_valid` - 企业ID索引
- `idx_{chainId}_company_node_establish_date` - 建立日期索引

### patent_node 表（每个产业链）
- `idx_{chainId}_patent_node_region_valid` - 地区筛选索引
- `idx_{chainId}_patent_node_type_date` - 专利类型+日期索引
- `idx_{chainId}_patent_node_node_valid` - 节点ID索引
- `idx_{chainId}_patent_node_ipc_valid` - 主IPC索引

## 🛠️ 工具说明

### Shell脚本工具
```bash
# 查看帮助
./scripts/create_indexes.sh --help

# 常用命令
./scripts/create_indexes.sh --list              # 查看产业链列表
./scripts/create_indexes.sh --all               # 创建所有索引
./scripts/create_indexes.sh --chain industry_ai # 为指定产业链创建索引
./scripts/create_indexes.sh --drop industry_ai  # 删除指定产业链索引
```

### API接口
- `GET /api/index-management/chain-list` - 获取产业链列表
- `GET /api/index-management/table-statistics` - 获取表统计信息
- `POST /api/index-management/create-all-indexes` - 创建所有索引
- `POST /api/index-management/create-company-node-indexes` - 创建company_node索引
- `POST /api/index-management/create-patent-node-indexes` - 创建patent_node索引
- `DELETE /api/index-management/drop-indexes/{chainId}` - 删除指定产业链索引

### SQL脚本
```bash
# 使用SQL脚本（手动执行）
mysql -h your_host -u your_user -p your_database < scripts/create_dynamic_table_indexes.sql
```

## ⚡ 执行示例

### 成功执行示例
```bash
$ ./scripts/create_indexes.sh --all
🚀 开始创建所有表的索引...
注意: 这可能需要几分钟时间，请耐心等待
发送请求: POST http://localhost:8612/api/index-management/create-all-indexes
{
  "status": "SUCCESS",
  "message": "所有动态表索引创建完成，请查看日志了解详细结果"
}
```

### 查看产业链列表
```bash
$ ./scripts/create_indexes.sh --list
📋 获取产业链列表...
{
  "status": "SUCCESS",
  "totalCount": 25,
  "chains": [
    {
      "nameEn": "industry_ai",
      "name": "人工智能",
      "label": "新兴产业"
    }
  ]
}
```

## 📈 预期效果

### 性能提升
- **地区筛选查询**: 80-90% 性能提升
- **节点+地区组合查询**: 70-85% 性能提升  
- **标签聚合查询**: 60-75% 性能提升

### 解决的问题
- 消除RegionFilterHelper相关的慢查询
- 减少数据库连接池耗尽问题
- 提高系统并发处理能力

## ⚠️ 注意事项

1. **执行时机**: 建议在业务低峰期执行
2. **磁盘空间**: 确保有足够空间存储索引
3. **执行时间**: 大表索引创建可能需要几分钟
4. **安全性**: 工具会自动检查表存在性，使用IF NOT EXISTS避免重复创建

## 🔍 故障排查

### 1. 检查服务状态
```bash
# 检查应用是否运行
curl -s http://localhost:8612/api/index-management/chain-list | head -1
```

### 2. 查看执行日志
```bash
# 查看应用日志
tail -f logs/app.log | grep -E "(索引|Index)"
```

### 3. 检查索引状态
```sql
-- 检查已创建的索引
SELECT table_name, index_name 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND index_name LIKE 'idx_%_company_node_%'
ORDER BY table_name;
```

## 📞 支持

如果遇到问题：
1. 查看应用日志：`logs/app.log`
2. 检查数据库连接
3. 确认产业链表是否存在
4. 联系开发团队

---

**重要提醒**: 这些索引将显著提升RegionFilterHelper相关查询的性能，建议优先创建company_node表的索引，因为它们对性能提升最为关键。

<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="CenterApplication-testk8s" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="testk8s" />
    <envs>
      <env name="file.preUrl" value="http://localhost:8612" />
    </envs>
    <module name="icir" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.quantchi.knowledge.center.CenterApplication" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.quantchi.knowledge.center.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/pom.xml" goal="clean" />
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>